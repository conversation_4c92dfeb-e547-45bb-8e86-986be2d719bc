package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	Browser BrowserConfig `yaml:"browser"`
	RTMP    RTMPConfig    `yaml:"rtmp"`
	Video   VideoConfig   `yaml:"video"`
}

// BrowserConfig 浏览器配置
type BrowserConfig struct {
	URL             string        `yaml:"url"`              // 要打开的网页URL
	Headless        bool          `yaml:"headless"`         // 是否无头模式
	Width           int           `yaml:"width"`            // 浏览器窗口宽度
	Height          int           `yaml:"height"`           // 浏览器窗口高度
	RecordingPath   string        `yaml:"recording_path"`   // 录制文件保存路径
	WaitTime        time.Duration `yaml:"wait_time"`        // 页面加载等待时间
	UserAgent       string        `yaml:"user_agent"`       // 用户代理
	ViewportWidth   int           `yaml:"viewport_width"`   // 视口宽度
	ViewportHeight  int           `yaml:"viewport_height"`  // 视口高度
}

// RTMPConfig RTMP推流配置
type RTMPConfig struct {
	ServerURL    string `yaml:"server_url"`    // RTMP服务器地址
	StreamKey    string `yaml:"stream_key"`    // 推流密钥
	Bitrate      int    `yaml:"bitrate"`       // 视频比特率
	FrameRate    int    `yaml:"frame_rate"`    // 帧率
	AudioBitrate int    `yaml:"audio_bitrate"` // 音频比特率
}

// VideoConfig 视频处理配置
type VideoConfig struct {
	Format     string `yaml:"format"`      // 视频格式 (mp4, webm等)
	Quality    string `yaml:"quality"`     // 视频质量 (high, medium, low)
	Duration   int    `yaml:"duration"`    // 录制时长(秒)，0表示无限制
	OutputPath string `yaml:"output_path"` // 输出文件路径
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Browser: BrowserConfig{
			URL:             "https://www.example.com",
			Headless:        false,
			Width:           1920,
			Height:          1080,
			RecordingPath:   "./recordings",
			WaitTime:        5 * time.Second,
			UserAgent:       "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			ViewportWidth:   1920,
			ViewportHeight:  1080,
		},
		RTMP: RTMPConfig{
			ServerURL:    "rtmp://localhost:1935/live",
			StreamKey:    "stream",
			Bitrate:      2000,
			FrameRate:    30,
			AudioBitrate: 128,
		},
		Video: VideoConfig{
			Format:     "mp4",
			Quality:    "high",
			Duration:   0, // 无限制
			OutputPath: "./output",
		},
	}
}

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
	// 如果文件不存在，创建默认配置文件
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		defaultCfg := DefaultConfig()
		if err := defaultCfg.SaveToFile(filename); err != nil {
			return nil, fmt.Errorf("failed to create default config: %w", err)
		}
		return defaultCfg, nil
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	return &config, nil
}

// SaveToFile 保存配置到文件
func (c *Config) SaveToFile(filename string) error {
	data, err := yaml.Marshal(c)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Browser.URL == "" {
		return fmt.Errorf("browser URL cannot be empty")
	}

	if c.RTMP.ServerURL == "" {
		return fmt.Errorf("RTMP server URL cannot be empty")
	}

	if c.Browser.Width <= 0 || c.Browser.Height <= 0 {
		return fmt.Errorf("browser dimensions must be positive")
	}

	if c.RTMP.Bitrate <= 0 || c.RTMP.FrameRate <= 0 {
		return fmt.Errorf("RTMP bitrate and frame rate must be positive")
	}

	return nil
}
