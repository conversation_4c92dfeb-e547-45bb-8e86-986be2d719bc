package main

import (
	"fmt"
	"log"

	"aigoplay/config"
	"aigoplay/browser"
	"aigoplay/streaming"
)

// 测试各个组件的基本功能
func main() {
	fmt.Println("=== Component Testing ===")

	// 1. 测试配置加载
	fmt.Println("\n1. Testing configuration loading...")
	cfg, err := config.LoadConfig("test-config.yaml")
	if err != nil {
		log.Printf("Failed to load config: %v", err)
		return
	}
	fmt.Printf("✓ Configuration loaded successfully")
	fmt.Printf("  - URL: %s\n", cfg.Browser.URL)
	fmt.Printf("  - RTMP: %s\n", cfg.RTMP.ServerURL)
	fmt.Printf("  - Viewport: %dx%d\n", cfg.Browser.ViewportWidth, cfg.Browser.ViewportHeight)

	// 2. 测试浏览器录制器初始化
	fmt.Println("\n2. Testing browser recorder initialization...")
	recorder := browser.NewRecorder(cfg)
	if recorder == nil {
		log.Printf("Failed to create browser recorder")
		return
	}
	fmt.Printf("✓ Browser recorder created successfully\n")

	// 3. 测试RTMP流媒体初始化
	fmt.Println("\n3. Testing RTMP streamer initialization...")
	streamer := streaming.NewRTMPStreamer(cfg)
	if streamer == nil {
		log.Printf("Failed to create RTMP streamer")
		return
	}
	fmt.Printf("✓ RTMP streamer created successfully\n")

	// 4. 测试配置验证
	fmt.Println("\n4. Testing configuration validation...")
	if err := cfg.Validate(); err != nil {
		log.Printf("Configuration validation failed: %v", err)
		return
	}
	fmt.Printf("✓ Configuration validation passed\n")

	// 5. 测试目录创建 (跳过 - 方法不存在)
	fmt.Println("\n5. Directory creation will be handled by the application\n")

	fmt.Println("\n=== All Component Tests Passed! ===")
	fmt.Println("\nNext steps:")
	fmt.Println("1. Install FFmpeg if not already installed")
	fmt.Println("2. Set up an RTMP server (e.g., nginx-rtmp, OBS Studio, or streaming service)")
	fmt.Println("3. Run the main application: ./aigoplay")
	fmt.Println("4. The application will:")
	fmt.Println("   - Download Playwright browser drivers (first run only)")
	fmt.Println("   - Open the specified URL in a browser")
	fmt.Println("   - Start recording the page")
	fmt.Println("   - Stream the recording to the RTMP server")

	fmt.Println("\nExample usage:")
	fmt.Println("  ./aigoplay -url \"https://www.youtube.com\" -rtmp \"rtmp://localhost:1935/live\" -key \"stream\"")
}
