package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aigoplay/browser"
	"aigoplay/config"
	"aigoplay/streaming"
)

// 基本功能测试
func main() {
	fmt.Println("=== Browser Recording and RTMP Streaming Test ===")

	// 创建测试配置
	cfg := &config.Config{
		Browser: config.BrowserConfig{
			URL:             "https://www.example.com",
			Headless:        true, // 测试时使用无头模式
			Width:           1280,
			Height:          720,
			RecordingPath:   "./test_recordings",
			WaitTime:        3 * time.Second,
			ViewportWidth:   1280,
			ViewportHeight:  720,
		},
		RTMP: config.RTMPConfig{
			ServerURL:    "rtmp://localhost:1935/live",
			StreamKey:    "test_stream",
			Bitrate:      1000,
			FrameRate:    25,
			AudioBitrate: 64,
		},
		Video: config.VideoConfig{
			Format:     "mp4",
			Quality:    "medium",
			Duration:   10, // 测试录制10秒
			OutputPath: "./test_output",
		},
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Configuration validation failed: %v", err)
	}
	fmt.Println("✓ Configuration validation passed")

	// 检查FFmpeg
	if err := streaming.CheckFFmpeg(); err != nil {
		log.Printf("⚠ FFmpeg check failed: %v", err)
		fmt.Println("  Note: FFmpeg is required for RTMP streaming")
	} else {
		fmt.Println("✓ FFmpeg check passed")
	}

	// 测试浏览器录制器
	fmt.Println("\n--- Testing Browser Recorder ---")
	
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	recorder := browser.NewRecorder(cfg)
	
	// 初始化浏览器
	fmt.Println("Initializing browser...")
	if err := recorder.Initialize(ctx); err != nil {
		log.Fatalf("Failed to initialize browser: %v", err)
	}
	fmt.Println("✓ Browser initialized successfully")

	// 开始录制
	fmt.Println("Starting recording...")
	if err := recorder.StartRecording(ctx); err != nil {
		log.Fatalf("Failed to start recording: %v", err)
	}
	fmt.Println("✓ Recording started successfully")

	// 录制几秒钟
	fmt.Println("Recording for 5 seconds...")
	time.Sleep(5 * time.Second)

	// 截图测试
	fmt.Println("Taking screenshot...")
	if err := recorder.TakeScreenshot("./test_screenshot.png"); err != nil {
		log.Printf("Screenshot failed: %v", err)
	} else {
		fmt.Println("✓ Screenshot saved successfully")
	}

	// 停止录制
	fmt.Println("Stopping recording...")
	if err := recorder.StopRecording(); err != nil {
		log.Printf("Failed to stop recording: %v", err)
	} else {
		fmt.Println("✓ Recording stopped successfully")
	}

	// 获取录制文件路径
	videoPath, err := recorder.GetRecordedVideoPath()
	if err != nil {
		log.Printf("Could not get video path: %v", err)
	} else {
		fmt.Printf("✓ Video recorded to: %s\n", videoPath)
	}

	// 清理浏览器资源
	if err := recorder.Cleanup(); err != nil {
		log.Printf("Cleanup failed: %v", err)
	} else {
		fmt.Println("✓ Browser cleanup completed")
	}

	// 测试RTMP推流器
	fmt.Println("\n--- Testing RTMP Streamer ---")
	
	streamer := streaming.NewRTMPStreamer(cfg)
	
	// 获取推流URL
	streamURL := streamer.GetStreamURL()
	fmt.Printf("Stream URL: %s\n", streamURL)

	// 获取统计信息
	stats := streamer.GetStats()
	fmt.Printf("Streaming stats: Bitrate=%d, FrameRate=%d, Running=%v\n", 
		stats.Bitrate, stats.FrameRate, stats.IsRunning)

	fmt.Println("✓ RTMP streamer test completed")

	// 测试配置文件保存/加载
	fmt.Println("\n--- Testing Configuration ---")
	
	testConfigFile := "./test_config.yaml"
	if err := cfg.SaveToFile(testConfigFile); err != nil {
		log.Printf("Failed to save config: %v", err)
	} else {
		fmt.Println("✓ Configuration saved successfully")
	}

	loadedCfg, err := config.LoadConfig(testConfigFile)
	if err != nil {
		log.Printf("Failed to load config: %v", err)
	} else {
		fmt.Println("✓ Configuration loaded successfully")
		if loadedCfg.Browser.URL == cfg.Browser.URL {
			fmt.Println("✓ Configuration data integrity verified")
		}
	}

	fmt.Println("\n=== Test Summary ===")
	fmt.Println("✓ Browser recording functionality works")
	fmt.Println("✓ Configuration management works")
	fmt.Println("✓ RTMP streamer setup works")
	
	if err := streaming.CheckFFmpeg(); err != nil {
		fmt.Println("⚠ FFmpeg not available - RTMP streaming will not work")
		fmt.Println("  Install FFmpeg to enable streaming functionality")
	} else {
		fmt.Println("✓ All dependencies available")
	}

	fmt.Println("\nTest completed successfully!")
	fmt.Println("You can now run the main program with: go run main.go")
}
