package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aigoplay/config"
	"aigoplay/mcp"
	"aigoplay/web"
)

func main() {
	fmt.Println("=== Integration Test ===")
	
	// Test 1: Configuration Loading
	fmt.Println("1. Testing configuration loading...")
	cfg, err := config.LoadConfig("test-config.yaml")
	if err != nil {
		log.Printf("Config test failed: %v", err)
	} else {
		fmt.Printf("✓ Configuration loaded successfully\n")
		fmt.Printf("  - Browser URL: %s\n", cfg.Browser.URL)
		fmt.Printf("  - RTMP Server: %s\n", cfg.RTMP.ServerURL)
	}
	
	// Test 2: Web Server Creation
	fmt.Println("\n2. Testing web server creation...")
	webServer := web.NewWebServer(cfg)
	if webServer != nil {
		fmt.Printf("✓ Web server created successfully\n")
	} else {
		fmt.Printf("✗ Web server creation failed\n")
	}
	
	// Test 3: MCP Server Creation
	fmt.Println("\n3. Testing MCP server creation...")
	mcpServer := mcp.NewPlaywrightMCPServer(cfg)
	if mcpServer != nil {
		fmt.Printf("✓ MCP server created successfully\n")
	} else {
		fmt.Printf("✗ MCP server creation failed\n")
	}
	
	// Test 4: Web Server Start/Stop (quick test)
	fmt.Println("\n4. Testing web server start/stop...")
	go func() {
		if err := webServer.Start(":18080"); err != nil {
			log.Printf("Web server start error: %v", err)
		}
	}()
	
	// Give it a moment to start
	time.Sleep(2 * time.Second)
	
	// Stop the server
	webServer.Stop()
	fmt.Printf("✓ Web server start/stop test completed\n")
	
	// Test 5: MCP Server Start/Stop (quick test)
	fmt.Println("\n5. Testing MCP server start/stop...")
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	
	go func() {
		if err := mcpServer.Start(); err != nil {
			log.Printf("MCP server start error: %v", err)
		}
	}()
	
	// Give it a moment to start
	time.Sleep(1 * time.Second)
	
	// Stop the server
	mcpServer.Stop()
	fmt.Printf("✓ MCP server start/stop test completed\n")
	
	fmt.Println("\n=== Integration Test Complete ===")
	fmt.Println("All components appear to be properly integrated!")
	fmt.Println("\nNext steps:")
	fmt.Println("1. Run 'go build' to compile the application")
	fmt.Println("2. Run './aigoplay -web' to start web interface")
	fmt.Println("3. Run './aigoplay -mcp' to start MCP server")
	fmt.Println("4. Run './aigoplay' for traditional CLI mode")
}
