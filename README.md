# Browser Recording and RTMP Streaming Tool

这是一个基于 Playwright-Go 和 go2rtc 的自动浏览器录制和 RTMP 推流工具。它可以自动打开指定的网页，录制浏览器页面，并将录制内容推流到 RTMP 服务器。

## 功能特性

- 🌐 自动打开指定网页并录制
- 📹 高质量视频录制
- 📡 实时 RTMP 推流
- ⚙️ 灵活的配置选项
- 🖥️ 支持桌面录制
- 📱 支持无头模式
- 🎛️ 命令行参数覆盖配置

## 依赖要求

### 系统要求
- Go 1.24+
- FFmpeg (用于视频处理和 RTMP 推流)
- Chrome/Chromium 浏览器 (Playwright 会自动下载)

### 安装 FFmpeg

**macOS:**
```bash
brew install ffmpeg
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install ffmpeg
```

**Windows:**
下载 FFmpeg 并添加到 PATH 环境变量

## 安装和编译

1. 克隆或下载项目
2. 安装依赖：
```bash
go mod tidy
```

3. 编译：
```bash
go build -o aigoplay
```

## 使用方法

### 基本使用

1. 运行程序（会自动创建默认配置文件）：
```bash
./aigoplay
```

2. 使用自定义参数：
```bash
./aigoplay -url "https://www.youtube.com" -rtmp "rtmp://localhost:1935/live" -key "mystream"
```

### 命令行参数

- `-config string`: 配置文件路径 (默认: "config.yaml")
- `-url string`: 要录制的网页URL (覆盖配置文件)
- `-rtmp string`: RTMP服务器地址 (覆盖配置文件)
- `-key string`: 推流密钥 (覆盖配置文件)
- `-duration int`: 录制时长(秒)，0表示无限制 (默认: 0)
- `-headless`: 无头模式
- `-help`: 显示帮助信息

### 使用示例

```bash
# 使用默认配置
./aigoplay

# 录制特定网页并推流
./aigoplay -url "https://www.bilibili.com" -rtmp "rtmp://live.example.com/live" -key "your_stream_key"

# 录制5分钟
./aigoplay -duration 300

# 无头模式录制
./aigoplay -headless

# 使用自定义配置文件
./aigoplay -config my_config.yaml
```

## 配置文件

程序使用 YAML 格式的配置文件。如果配置文件不存在，程序会自动创建一个默认配置文件。

### 配置文件结构

```yaml
browser:
  url: "https://www.example.com"          # 要录制的网页URL
  headless: false                         # 是否无头模式
  width: 1920                            # 浏览器窗口宽度
  height: 1080                           # 浏览器窗口高度
  recording_path: "./recordings"          # 录制文件保存路径
  wait_time: 5s                          # 页面加载等待时间
  user_agent: "Mozilla/5.0..."           # 用户代理
  viewport_width: 1920                   # 视口宽度
  viewport_height: 1080                  # 视口高度

rtmp:
  server_url: "rtmp://localhost:1935/live" # RTMP服务器地址
  stream_key: "stream"                     # 推流密钥
  bitrate: 2000                           # 视频比特率 (kbps)
  frame_rate: 30                          # 帧率
  audio_bitrate: 128                      # 音频比特率 (kbps)

video:
  format: "mp4"                          # 视频格式
  quality: "high"                        # 视频质量
  duration: 0                            # 录制时长(秒)，0表示无限制
  output_path: "./output"                # 输出文件路径
```

## 工作原理

1. **浏览器录制**: 使用 Playwright-Go 启动 Chrome 浏览器，导航到指定URL并开始录制
2. **视频处理**: 录制的视频文件通过 FFmpeg 进行处理
3. **RTMP推流**: 使用 FFmpeg 将处理后的视频推流到指定的 RTMP 服务器

## 常见问题

### Q: 程序启动失败，提示 "FFmpeg not found"
A: 请确保已安装 FFmpeg 并添加到系统 PATH 环境变量中。

### Q: 浏览器无法启动
A: Playwright 会自动下载 Chrome 浏览器。如果下载失败，可以手动运行：
```bash
go run github.com/playwright-community/playwright-go/cmd/playwright install
```

### Q: RTMP 推流失败
A: 请检查：
- RTMP 服务器地址是否正确
- 推流密钥是否正确
- 网络连接是否正常
- RTMP 服务器是否支持接收推流

### Q: 录制的视频质量不好
A: 可以调整配置文件中的以下参数：
- `rtmp.bitrate`: 增加视频比特率
- `rtmp.frame_rate`: 调整帧率
- `browser.viewport_width/height`: 调整录制分辨率

## 开发和扩展

项目结构：
```
.
├── main.go              # 主程序入口
├── config/
│   └── config.go        # 配置管理
├── browser/
│   └── recorder.go      # 浏览器录制模块
├── streaming/
│   └── rtmp.go          # RTMP推流模块
├── config.yaml          # 配置文件
└── README.md           # 说明文档
```

## 许可证

本项目基于 MIT 许可证开源。

## 贡献

欢迎提交 Issue 和 Pull Request！
