package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/metoro-io/mcp-golang/mcp"
	"github.com/metoro-io/mcp-golang/server"

	"aigoplay/browser"
	"aigoplay/config"
	"aigoplay/streaming"
)

type PlaywrightMCPServer struct {
	server   *server.MCPServer
	config   *config.Config
	recorder *browser.Recorder
	streamer *streaming.RTMPStreamer
	ctx      context.Context
	cancel   context.CancelFunc
}

type RecordingRequest struct {
	URL      string `json:"url"`
	Duration int    `json:"duration,omitempty"`
	Headless bool   `json:"headless,omitempty"`
}

type StreamingRequest struct {
	RTMPServer string `json:"rtmp_server"`
	StreamKey  string `json:"stream_key,omitempty"`
}

func NewPlaywrightMCPServer(cfg *config.Config) *PlaywrightMCPServer {
	ctx, cancel := context.WithCancel(context.Background())
	
	mcpServer := &PlaywrightMCPServer{
		config: cfg,
		ctx:    ctx,
		cancel: cancel,
	}
	
	// Create MCP server
	mcpServer.server = server.NewMCPServer(
		"playwright-streaming-server",
		"1.0.0",
		server.WithLogging(),
	)
	
	mcpServer.setupTools()
	mcpServer.setupResources()
	
	return mcpServer
}

func (s *PlaywrightMCPServer) setupTools() {
	// Tool: Start Recording
	s.server.AddTool(mcp.Tool{
		Name:        "start_recording",
		Description: "Start browser recording with specified URL and options",
		InputSchema: mcp.ToolInputSchema{
			Type: "object",
			Properties: map[string]mcp.ToolInputProperty{
				"url": {
					Type:        "string",
					Description: "URL to record",
				},
				"duration": {
					Type:        "integer",
					Description: "Recording duration in seconds (0 for unlimited)",
				},
				"headless": {
					Type:        "boolean",
					Description: "Run browser in headless mode",
				},
			},
			Required: []string{"url"},
		},
	}, s.handleStartRecording)
	
	// Tool: Stop Recording
	s.server.AddTool(mcp.Tool{
		Name:        "stop_recording",
		Description: "Stop current browser recording",
		InputSchema: mcp.ToolInputSchema{
			Type:       "object",
			Properties: map[string]mcp.ToolInputProperty{},
		},
	}, s.handleStopRecording)
	
	// Tool: Start Streaming
	s.server.AddTool(mcp.Tool{
		Name:        "start_streaming",
		Description: "Start RTMP streaming to specified server",
		InputSchema: mcp.ToolInputSchema{
			Type: "object",
			Properties: map[string]mcp.ToolInputProperty{
				"rtmp_server": {
					Type:        "string",
					Description: "RTMP server URL",
				},
				"stream_key": {
					Type:        "string",
					Description: "Stream key for authentication",
				},
			},
			Required: []string{"rtmp_server"},
		},
	}, s.handleStartStreaming)
	
	// Tool: Stop Streaming
	s.server.AddTool(mcp.Tool{
		Name:        "stop_streaming",
		Description: "Stop current RTMP streaming",
		InputSchema: mcp.ToolInputSchema{
			Type:       "object",
			Properties: map[string]mcp.ToolInputProperty{},
		},
	}, s.handleStopStreaming)
	
	// Tool: Get Status
	s.server.AddTool(mcp.Tool{
		Name:        "get_status",
		Description: "Get current recording and streaming status",
		InputSchema: mcp.ToolInputSchema{
			Type:       "object",
			Properties: map[string]mcp.ToolInputProperty{},
		},
	}, s.handleGetStatus)
	
	// Tool: Take Screenshot
	s.server.AddTool(mcp.Tool{
		Name:        "take_screenshot",
		Description: "Take a screenshot of the current page",
		InputSchema: mcp.ToolInputSchema{
			Type: "object",
			Properties: map[string]mcp.ToolInputProperty{
				"path": {
					Type:        "string",
					Description: "Path to save screenshot",
				},
			},
		},
	}, s.handleTakeScreenshot)
}

func (s *PlaywrightMCPServer) setupResources() {
	// Resource: Current Configuration
	s.server.AddResource(mcp.Resource{
		URI:         "config://current",
		Name:        "Current Configuration",
		Description: "Current server configuration",
		MimeType:    "application/json",
	}, s.handleGetConfigResource)
	
	// Resource: Recording Status
	s.server.AddResource(mcp.Resource{
		URI:         "status://recording",
		Name:        "Recording Status",
		Description: "Current recording status and information",
		MimeType:    "application/json",
	}, s.handleGetRecordingStatusResource)
	
	// Resource: Streaming Status
	s.server.AddResource(mcp.Resource{
		URI:         "status://streaming",
		Name:        "Streaming Status",
		Description: "Current streaming status and information",
		MimeType:    "application/json",
	}, s.handleGetStreamingStatusResource)
}

func (s *PlaywrightMCPServer) handleStartRecording(arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	var req RecordingRequest
	
	// Parse arguments
	if url, ok := arguments["url"].(string); ok {
		req.URL = url
	} else {
		return &mcp.CallToolResult{
			IsError: true,
			Content: []mcp.CallToolResultContent{{
				Type: "text",
				Text: "URL is required",
			}},
		}, nil
	}
	
	if duration, ok := arguments["duration"].(float64); ok {
		req.Duration = int(duration)
	}
	
	if headless, ok := arguments["headless"].(bool); ok {
		req.Headless = headless
	}
	
	// Update configuration
	s.config.Browser.URL = req.URL
	if req.Duration > 0 {
		s.config.Video.Duration = req.Duration
	}
	s.config.Browser.Headless = req.Headless
	
	// Stop any existing recording
	if s.recorder != nil {
		s.recorder.Stop()
		s.recorder = nil
	}
	
	// Create new recorder
	var err error
	s.recorder, err = browser.NewRecorder(s.config)
	if err != nil {
		return &mcp.CallToolResult{
			IsError: true,
			Content: []mcp.CallToolResultContent{{
				Type: "text",
				Text: fmt.Sprintf("Failed to create recorder: %v", err),
			}},
		}, nil
	}
	
	// Start recording in background
	go func() {
		if err := s.recorder.Start(s.ctx); err != nil {
			log.Printf("Recording failed: %v", err)
		}
	}()
	
	return &mcp.CallToolResult{
		Content: []mcp.CallToolResultContent{{
			Type: "text",
			Text: fmt.Sprintf("Recording started for URL: %s", req.URL),
		}},
	}, nil
}

func (s *PlaywrightMCPServer) handleStopRecording(arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	if s.recorder != nil {
		s.recorder.Stop()
		s.recorder = nil
		
		return &mcp.CallToolResult{
			Content: []mcp.CallToolResultContent{{
				Type: "text",
				Text: "Recording stopped successfully",
			}},
		}, nil
	}
	
	return &mcp.CallToolResult{
		Content: []mcp.CallToolResultContent{{
			Type: "text",
			Text: "No active recording to stop",
		}},
	}, nil
}

func (s *PlaywrightMCPServer) handleStartStreaming(arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	var req StreamingRequest
	
	if rtmpServer, ok := arguments["rtmp_server"].(string); ok {
		req.RTMPServer = rtmpServer
	} else {
		return &mcp.CallToolResult{
			IsError: true,
			Content: []mcp.CallToolResultContent{{
				Type: "text",
				Text: "RTMP server URL is required",
			}},
		}, nil
	}
	
	if streamKey, ok := arguments["stream_key"].(string); ok {
		req.StreamKey = streamKey
	}
	
	// Update configuration
	s.config.RTMP.ServerURL = req.RTMPServer
	if req.StreamKey != "" {
		s.config.RTMP.StreamKey = req.StreamKey
	}
	
	// Stop any existing streaming
	if s.streamer != nil {
		s.streamer.Stop()
		s.streamer = nil
	}
	
	// Create new streamer
	var err error
	s.streamer, err = streaming.NewRTMPStreamer(s.config)
	if err != nil {
		return &mcp.CallToolResult{
			IsError: true,
			Content: []mcp.CallToolResultContent{{
				Type: "text",
				Text: fmt.Sprintf("Failed to create streamer: %v", err),
			}},
		}, nil
	}
	
	// Start streaming in background
	go func() {
		time.Sleep(3 * time.Second)
		if err := s.streamer.StreamDesktop(s.ctx); err != nil {
			log.Printf("Streaming failed: %v", err)
		}
	}()
	
	return &mcp.CallToolResult{
		Content: []mcp.CallToolResultContent{{
			Type: "text",
			Text: fmt.Sprintf("Streaming started to: %s", req.RTMPServer),
		}},
	}, nil
}
