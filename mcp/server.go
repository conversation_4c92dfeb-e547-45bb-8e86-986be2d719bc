package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"aigoplay/browser"
	"aigoplay/config"
	"aigoplay/streaming"
)

// PlaywrightMCPServer provides a simple HTTP-based MCP-like interface
type PlaywrightMCPServer struct {
	server   *http.Server
	config   *config.Config
	recorder *browser.Recorder
	streamer *streaming.RTMPStreamer
	ctx      context.Context
	cancel   context.CancelFunc
}

type MCPRequest struct {
	Method string                 `json:"method"`
	Params map[string]interface{} `json:"params"`
}

type MCPResponse struct {
	Success bool        `json:"success"`
	Result  interface{} `json:"result,omitempty"`
	Error   string      `json:"error,omitempty"`
}

func NewPlaywrightMCPServer(cfg *config.Config) *PlaywrightMCPServer {
	ctx, cancel := context.WithCancel(context.Background())

	mcpServer := &PlaywrightMCPServer{
		config: cfg,
		ctx:    ctx,
		cancel: cancel,
	}

	// Create HTTP server
	mux := http.NewServeMux()
	mux.HandleFunc("/mcp", mcpServer.handleMCPRequest)
	mux.HandleFunc("/status", mcpServer.handleStatus)
	mux.HandleFunc("/health", mcpServer.handleHealth)

	mcpServer.server = &http.Server{
		Addr:    ":8082",
		Handler: mux,
	}

	return mcpServer
}

func (s *PlaywrightMCPServer) handleMCPRequest(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req MCPRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		response := MCPResponse{Success: false, Error: "Invalid JSON"}
		json.NewEncoder(w).Encode(response)
		return
	}

	var response MCPResponse

	switch req.Method {
	case "start_recording":
		response = s.handleStartRecording(req.Params)
	case "stop_recording":
		response = s.handleStopRecording(req.Params)
	case "start_streaming":
		response = s.handleStartStreaming(req.Params)
	case "stop_streaming":
		response = s.handleStopStreaming(req.Params)
	case "get_status":
		response = s.handleGetStatus(req.Params)
	case "take_screenshot":
		response = s.handleTakeScreenshot(req.Params)
	default:
		response = MCPResponse{Success: false, Error: "Unknown method: " + req.Method}
	}

	json.NewEncoder(w).Encode(response)
}

func (s *PlaywrightMCPServer) handleStatus(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	status := map[string]interface{}{
		"recording":   s.recorder != nil,
		"streaming":   s.streamer != nil,
		"url":         s.config.Browser.URL,
		"duration":    s.config.Video.Duration,
		"headless":    s.config.Browser.Headless,
		"rtmp_server": s.config.RTMP.ServerURL,
		"stream_key":  s.config.RTMP.StreamKey,
		"timestamp":   time.Now().Format(time.RFC3339),
	}

	json.NewEncoder(w).Encode(status)
}

func (s *PlaywrightMCPServer) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
		"version":   "1.0.0",
	}

	json.NewEncoder(w).Encode(health)
}

// Handler functions for MCP methods
func (s *PlaywrightMCPServer) handleStartRecording(params map[string]interface{}) MCPResponse {
	url, ok := params["url"].(string)
	if !ok {
		return MCPResponse{Success: false, Error: "URL parameter is required"}
	}

	// Update config with provided parameters
	if duration, ok := params["duration"].(float64); ok {
		s.config.Video.Duration = int(duration)
	}
	if headless, ok := params["headless"].(bool); ok {
		s.config.Browser.Headless = headless
	}
	s.config.Browser.URL = url

	// Get RTMP URL parameter (for streaming recording)
	rtmpURL, hasRTMP := params["rtmp_url"].(string)
	if !hasRTMP {
		rtmpURL = "rtmp://localhost:1935/live/stream" // Default RTMP URL
	}

	// Create recorder if not exists
	if s.recorder == nil {
		s.recorder = browser.NewRecorder(s.config)
	}

	// Initialize recorder
	if err := s.recorder.Initialize(s.ctx); err != nil {
		return MCPResponse{Success: false, Error: fmt.Sprintf("Failed to initialize recorder: %v", err)}
	}

	// Start streaming recording (旁路录制)
	if err := s.recorder.StartStreamingToRTMP(s.ctx, rtmpURL); err != nil {
		return MCPResponse{Success: false, Error: fmt.Sprintf("Failed to start streaming recording: %v", err)}
	}

	return MCPResponse{
		Success: true,
		Result: map[string]interface{}{
			"message":  "Streaming recording started successfully",
			"url":      url,
			"rtmp_url": rtmpURL,
		},
	}
}

func (s *PlaywrightMCPServer) handleStopRecording(params map[string]interface{}) MCPResponse {
	if s.recorder == nil {
		return MCPResponse{Success: false, Error: "No active recording to stop"}
	}

	if err := s.recorder.StopRecording(); err != nil {
		return MCPResponse{Success: false, Error: fmt.Sprintf("Failed to stop recording: %v", err)}
	}

	// Clean up recorder
	if err := s.recorder.Cleanup(); err != nil {
		log.Printf("Warning: Failed to cleanup recorder: %v", err)
	}
	s.recorder = nil

	return MCPResponse{
		Success: true,
		Result:  map[string]interface{}{"message": "Recording stopped successfully"},
	}
}

func (s *PlaywrightMCPServer) handleStartStreaming(params map[string]interface{}) MCPResponse {
	rtmpServer, ok := params["rtmp_server"].(string)
	if !ok {
		return MCPResponse{Success: false, Error: "rtmp_server parameter is required"}
	}

	// Update config
	s.config.RTMP.ServerURL = rtmpServer
	if streamKey, ok := params["stream_key"].(string); ok {
		s.config.RTMP.StreamKey = streamKey
	}

	// Create streamer if not exists
	if s.streamer == nil {
		s.streamer = streaming.NewRTMPStreamer(s.config)
	}

	// Start streaming
	if err := s.streamer.StreamDesktop(s.ctx); err != nil {
		return MCPResponse{Success: false, Error: fmt.Sprintf("Failed to start streaming: %v", err)}
	}

	return MCPResponse{
		Success: true,
		Result: map[string]interface{}{
			"message":     "Streaming started successfully",
			"rtmp_server": rtmpServer,
		},
	}
}

func (s *PlaywrightMCPServer) handleStopStreaming(params map[string]interface{}) MCPResponse {
	if s.streamer == nil {
		return MCPResponse{Success: false, Error: "No active streaming to stop"}
	}

	if err := s.streamer.StopStreaming(); err != nil {
		return MCPResponse{Success: false, Error: fmt.Sprintf("Failed to stop streaming: %v", err)}
	}

	s.streamer = nil

	return MCPResponse{
		Success: true,
		Result:  map[string]interface{}{"message": "Streaming stopped successfully"},
	}
}

func (s *PlaywrightMCPServer) handleGetStatus(params map[string]interface{}) MCPResponse {
	status := map[string]interface{}{
		"recording":   s.recorder != nil,
		"streaming":   s.streamer != nil,
		"url":         s.config.Browser.URL,
		"duration":    s.config.Video.Duration,
		"headless":    s.config.Browser.Headless,
		"rtmp_server": s.config.RTMP.ServerURL,
		"stream_key":  s.config.RTMP.StreamKey,
	}

	return MCPResponse{
		Success: true,
		Result:  status,
	}
}

func (s *PlaywrightMCPServer) handleTakeScreenshot(params map[string]interface{}) MCPResponse {
	if s.recorder == nil {
		return MCPResponse{Success: false, Error: "No active recording session"}
	}

	path := "./screenshot.png"
	if pathParam, ok := params["path"].(string); ok {
		path = pathParam
	}

	// Take screenshot using the recorder
	if err := s.recorder.TakeScreenshot(path); err != nil {
		return MCPResponse{Success: false, Error: fmt.Sprintf("Failed to take screenshot: %v", err)}
	}

	return MCPResponse{
		Success: true,
		Result:  map[string]interface{}{"message": "Screenshot saved", "path": path},
	}
}

func (s *PlaywrightMCPServer) Start() error {
	log.Printf("Starting Playwright MCP Server on %s...", s.server.Addr)
	return s.server.ListenAndServe()
}

func (s *PlaywrightMCPServer) Stop() {
	log.Println("Stopping Playwright MCP Server...")

	// Stop recording and streaming
	if s.recorder != nil {
		s.recorder.StopRecording()
		s.recorder.Cleanup()
	}
	if s.streamer != nil {
		s.streamer.StopStreaming()
	}

	// Cancel context
	s.cancel()

	// Stop HTTP server
	if s.server != nil {
		s.server.Shutdown(s.ctx)
	}
}
