package rtmp

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"

	"aigoplay/config"
)

// RTMPServer provides a simple RTMP-like server for receiving streams
type RTMPServer struct {
	config     *config.Config
	listener   net.Listener
	httpServer *http.Server
	streams    map[string]*Stream
	mutex      sync.RWMutex
	ctx        context.Context
	cancel     context.CancelFunc
	running    bool
}

// Stream represents an active stream
type Stream struct {
	Key       string
	StartTime time.Time
	Data      []byte
	mutex     sync.RWMutex
}

// NewRTMPServer creates a new RTMP server
func NewRTMPServer(cfg *config.Config) *RTMPServer {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &RTMPServer{
		config:  cfg,
		streams: make(map[string]*Stream),
		ctx:     ctx,
		cancel:  cancel,
	}
}

// Start starts the RTMP server
func (s *RTMPServer) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if s.running {
		return fmt.Errorf("RTMP server is already running")
	}

	// Create HLS output directory
	hlsDir := "/tmp/hls"
	if err := os.MkdirAll(hlsDir, 0755); err != nil {
		return fmt.Errorf("failed to create HLS directory: %v", err)
	}

	// Start HTTP server for HLS delivery
	mux := http.NewServeMux()
	mux.HandleFunc("/hls/", s.handleHLS)
	mux.HandleFunc("/live/", s.handleLiveStream)
	mux.HandleFunc("/status", s.handleStatus)
	
	s.httpServer = &http.Server{
		Addr:    ":8080",
		Handler: mux,
	}

	// Start HTTP server in goroutine
	go func() {
		log.Printf("Starting HLS HTTP server on :8080")
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("HTTP server error: %v", err)
		}
	}()

	// Start TCP listener for RTMP-like protocol (simplified)
	var err error
	s.listener, err = net.Listen("tcp", ":1935")
	if err != nil {
		return fmt.Errorf("failed to start RTMP listener: %v", err)
	}

	s.running = true
	
	// Accept connections in goroutine
	go s.acceptConnections()
	
	log.Println("RTMP server started on port 1935")
	log.Println("HLS endpoint available at http://localhost:8080/hls/")
	
	return nil
}

// Stop stops the RTMP server
func (s *RTMPServer) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if !s.running {
		return fmt.Errorf("RTMP server is not running")
	}

	s.cancel()
	s.running = false

	if s.listener != nil {
		s.listener.Close()
	}

	if s.httpServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		s.httpServer.Shutdown(ctx)
	}

	log.Println("RTMP server stopped")
	return nil
}

// IsRunning returns whether the server is running
func (s *RTMPServer) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.running
}

// acceptConnections accepts incoming RTMP connections
func (s *RTMPServer) acceptConnections() {
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			conn, err := s.listener.Accept()
			if err != nil {
				if s.running {
					log.Printf("Error accepting connection: %v", err)
				}
				continue
			}
			
			go s.handleConnection(conn)
		}
	}
}

// handleConnection handles an individual RTMP connection
func (s *RTMPServer) handleConnection(conn net.Conn) {
	defer conn.Close()
	
	log.Printf("New RTMP connection from %s", conn.RemoteAddr())
	
	// Simple protocol: read stream key and data
	buffer := make([]byte, 4096)
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			n, err := conn.Read(buffer)
			if err != nil {
				log.Printf("Connection read error: %v", err)
				return
			}
			
			// Process received data (simplified)
			s.processStreamData(buffer[:n])
		}
	}
}

// processStreamData processes incoming stream data
func (s *RTMPServer) processStreamData(data []byte) {
	// This is a simplified implementation
	// In a real RTMP server, you would parse the RTMP protocol
	log.Printf("Received %d bytes of stream data", len(data))
}

// handleHLS serves HLS files
func (s *RTMPServer) handleHLS(w http.ResponseWriter, r *http.Request) {
	// Extract file path
	filePath := r.URL.Path[5:] // Remove "/hls/" prefix
	fullPath := filepath.Join("/tmp/hls", filePath)
	
	// Check if file exists
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		http.NotFound(w, r)
		return
	}
	
	// Set appropriate content type
	if filepath.Ext(filePath) == ".m3u8" {
		w.Header().Set("Content-Type", "application/vnd.apple.mpegurl")
	} else if filepath.Ext(filePath) == ".ts" {
		w.Header().Set("Content-Type", "video/mp2t")
	}
	
	// Enable CORS
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	
	if r.Method == "OPTIONS" {
		return
	}
	
	http.ServeFile(w, r, fullPath)
}

// handleLiveStream handles live stream requests
func (s *RTMPServer) handleLiveStream(w http.ResponseWriter, r *http.Request) {
	streamKey := r.URL.Path[6:] // Remove "/live/" prefix
	
	s.mutex.RLock()
	stream, exists := s.streams[streamKey]
	s.mutex.RUnlock()
	
	if !exists {
		http.NotFound(w, r)
		return
	}
	
	w.Header().Set("Content-Type", "video/mp4")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	stream.mutex.RLock()
	w.Write(stream.Data)
	stream.mutex.RUnlock()
}

// handleStatus returns server status
func (s *RTMPServer) handleStatus(w http.ResponseWriter, r *http.Request) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	status := map[string]interface{}{
		"running":      s.running,
		"active_streams": len(s.streams),
		"timestamp":    time.Now().Format(time.RFC3339),
	}
	
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	// Simple JSON encoding
	fmt.Fprintf(w, `{"running":%t,"active_streams":%d,"timestamp":"%s"}`, 
		status["running"], status["active_streams"], status["timestamp"])
}

// GetStreamURL returns the RTMP stream URL
func (s *RTMPServer) GetStreamURL() string {
	return "rtmp://localhost:1935/live"
}

// GetHLSURL returns the HLS playback URL
func (s *RTMPServer) GetHLSURL(streamKey string) string {
	return fmt.Sprintf("http://localhost:8080/hls/%s.m3u8", streamKey)
}
