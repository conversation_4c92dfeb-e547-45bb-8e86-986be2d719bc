package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"aigoplay/browser"
	"aigoplay/config"
	"aigoplay/mcp"
	"aigoplay/streaming"
	"aigoplay/web"
)

const (
	defaultConfigFile = "config.yaml"
)

func main() {
	// 命令行参数
	var (
		configFile = flag.String("config", defaultConfigFile, "配置文件路径")
		url        = flag.String("url", "", "要录制的网页URL (覆盖配置文件)")
		rtmpURL    = flag.String("rtmp", "", "RTMP服务器地址 (覆盖配置文件)")
		streamKey  = flag.String("key", "", "推流密钥 (覆盖配置文件)")
		duration   = flag.Int("duration", 0, "录制时长(秒)，0表示无限制")
		headless   = flag.Bool("headless", false, "无头模式")
		webMode    = flag.Bool("web", false, "启动Web界面模式")
		webPort    = flag.String("port", "8080", "Web服务器端口")
		mcpMode    = flag.Bool("mcp", false, "启动MCP服务器模式")
		help       = flag.Bool("help", false, "显示帮助信息")
	)
	flag.Parse()

	if *help {
		printUsage()
		return
	}

	// 加载配置
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 命令行参数覆盖配置
	if *url != "" {
		cfg.Browser.URL = *url
	}
	if *rtmpURL != "" {
		cfg.RTMP.ServerURL = *rtmpURL
	}
	if *streamKey != "" {
		cfg.RTMP.StreamKey = *streamKey
	}
	if *duration > 0 {
		cfg.Video.Duration = *duration
	}
	if *headless {
		cfg.Browser.Headless = true
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Invalid configuration: %v", err)
	}

	// 根据模式启动不同的服务
	if *webMode {
		startWebMode(cfg, *webPort)
		return
	}

	if *mcpMode {
		startMCPMode(cfg)
		return
	}

	// 检查FFmpeg
	if err := streaming.CheckFFmpeg(); err != nil {
		log.Fatalf("FFmpeg check failed: %v", err)
	}

	log.Printf("Starting browser recording and RTMP streaming...")
	log.Printf("Target URL: %s", cfg.Browser.URL)
	log.Printf("RTMP Server: %s", cfg.RTMP.ServerURL)

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 创建录制器和推流器
	recorder := browser.NewRecorder(cfg)
	streamer := streaming.NewRTMPStreamer(cfg)

	// 清理函数
	cleanup := func() {
		log.Printf("Cleaning up...")
		
		if streamer.IsRunning() {
			if err := streamer.StopStreaming(); err != nil {
				log.Printf("Error stopping streamer: %v", err)
			}
		}

		if err := recorder.Cleanup(); err != nil {
			log.Printf("Error cleaning up recorder: %v", err)
		}
	}
	defer cleanup()

	// 初始化浏览器
	log.Printf("Initializing browser...")
	if err := recorder.Initialize(ctx); err != nil {
		log.Fatalf("Failed to initialize browser: %v", err)
	}

	// 开始录制
	log.Printf("Starting browser recording...")
	if err := recorder.StartRecording(ctx); err != nil {
		log.Fatalf("Failed to start recording: %v", err)
	}

	// 等待一段时间让录制稳定
	time.Sleep(3 * time.Second)

	// 获取录制的视频文件路径
	videoPath, err := recorder.GetRecordedVideoPath()
	if err != nil {
		log.Printf("Warning: Could not get video path: %v", err)
		// 如果无法获取视频路径，尝试直接推流桌面
		log.Printf("Attempting to stream desktop directly...")
		if err := streamer.StreamDesktop(ctx); err != nil {
			log.Fatalf("Failed to start desktop streaming: %v", err)
		}
	} else {
		log.Printf("Video will be saved to: %s", videoPath)
		
		// 等待视频文件创建
		log.Printf("Waiting for video file to be created...")
		for i := 0; i < 30; i++ { // 最多等待30秒
			if _, err := os.Stat(videoPath); err == nil {
				break
			}
			time.Sleep(1 * time.Second)
		}

		// 开始推流
		log.Printf("Starting RTMP streaming...")
		if err := streamer.StreamFromFile(ctx, videoPath); err != nil {
			log.Printf("Failed to start file streaming: %v", err)
			log.Printf("Attempting to stream desktop directly...")
			if err := streamer.StreamDesktop(ctx); err != nil {
				log.Fatalf("Failed to start desktop streaming: %v", err)
			}
		}
	}

	log.Printf("Recording and streaming started successfully!")
	log.Printf("Press Ctrl+C to stop...")

	// 如果设置了录制时长，创建定时器
	var timer *time.Timer
	if cfg.Video.Duration > 0 {
		timer = time.NewTimer(time.Duration(cfg.Video.Duration) * time.Second)
		log.Printf("Recording will stop automatically after %d seconds", cfg.Video.Duration)
	}

	// 等待信号或定时器
	if timer != nil {
		select {
		case sig := <-sigChan:
			log.Printf("Received signal: %v", sig)
		case <-timer.C:
			log.Printf("Recording duration reached")
		case <-ctx.Done():
			log.Printf("Context cancelled")
		}
	} else {
		select {
		case sig := <-sigChan:
			log.Printf("Received signal: %v", sig)
		case <-ctx.Done():
			log.Printf("Context cancelled")
		}
	}

	log.Printf("Stopping recording and streaming...")
}

func startWebMode(cfg *config.Config, port string) {
	log.Printf("Starting Web Interface Mode on port %s...", port)

	webServer := web.NewWebServer(cfg)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 转换端口为整数
	portInt, err := strconv.Atoi(port)
	if err != nil {
		log.Fatalf("Invalid port number: %v", err)
	}

	// 启动Web服务器
	go func() {
		if err := webServer.Start(portInt); err != nil {
			log.Fatalf("Failed to start web server: %v", err)
		}
	}()

	log.Printf("Web interface available at: http://localhost:%s", port)
	log.Printf("Press Ctrl+C to stop...")

	// 等待信号
	<-sigChan
	log.Printf("Shutting down web server...")
	webServer.Stop()
}

func startMCPMode(cfg *config.Config) {
	log.Printf("Starting MCP Server Mode...")

	mcpServer := mcp.NewPlaywrightMCPServer(cfg)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动MCP服务器
	go func() {
		if err := mcpServer.Start(); err != nil {
			log.Fatalf("Failed to start MCP server: %v", err)
		}
	}()

	log.Printf("MCP server started successfully")
	log.Printf("Press Ctrl+C to stop...")

	// 等待信号
	<-sigChan
	log.Printf("Shutting down MCP server...")
	mcpServer.Stop()
}

func printUsage() {
	fmt.Printf(`
Browser Recording and RTMP Streaming Tool

Usage: %s [options]

Options:
  -config string    配置文件路径 (default "config.yaml")
  -url string       要录制的网页URL (覆盖配置文件)
  -rtmp string      RTMP服务器地址 (覆盖配置文件)
  -key string       推流密钥 (覆盖配置文件)
  -duration int     录制时长(秒)，0表示无限制 (default 0)
  -headless         无头模式
  -web              启动Web界面模式
  -port string      Web服务器端口 (default "8080")
  -mcp              启动MCP服务器模式
  -help             显示帮助信息

Modes:
  1. Command Line Mode (default): 直接录制和推流
  2. Web Interface Mode (-web): 启动Web界面进行控制
  3. MCP Server Mode (-mcp): 启动MCP服务器供外部控制

Examples:
  # 使用默认配置 (命令行模式)
  %s

  # 指定URL和RTMP服务器
  %s -url "https://www.youtube.com" -rtmp "rtmp://localhost:1935/live" -key "stream"

  # 录制5分钟
  %s -duration 300

  # 无头模式录制
  %s -headless

  # 启动Web界面
  %s -web

  # 启动Web界面在指定端口
  %s -web -port 9090

  # 启动MCP服务器
  %s -mcp

Configuration:
  程序会在当前目录查找 config.yaml 配置文件。
  如果文件不存在，会自动创建一个默认配置文件。

Requirements:
  - FFmpeg (用于视频处理和RTMP推流)
  - Chrome/Chromium 浏览器 (Playwright会自动下载)
  - nginx-rtmp (用于Web模式的RTMP服务器)

`, os.Args[0], os.Args[0], os.Args[0], os.Args[0], os.Args[0], os.Args[0], os.Args[0], os.Args[0])
}
