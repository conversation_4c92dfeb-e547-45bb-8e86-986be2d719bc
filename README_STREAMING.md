# 旁路录制功能说明

## 概述

本项目已成功实现旁路录制功能，支持直接将浏览器内容推流到RTMP服务器，无需先录制到文件再推流。这种方式具有更低的延迟和更高的效率。

## 核心功能

### 1. 旁路录制 (Bypass Recording)
- **直接推流**: 浏览器内容直接通过FFmpeg推流到RTMP服务器
- **无文件录制**: 不生成本地视频文件，减少磁盘IO
- **实时传输**: 最小化延迟，适合直播场景
- **自动适配**: 支持macOS (avfoundation) 和 Linux (x11grab)

### 2. 纯Go实现
- **无nginx依赖**: 完全基于Go实现的RTMP服务器
- **内置HLS支持**: 自动生成HLS流供Web播放
- **并发安全**: 使用Go的并发特性处理多路流

### 3. 多种运行模式
- **命令行模式**: `./aigoplay` - 直接录制和推流
- **Web界面模式**: `./aigoplay -web` - 通过Web界面控制
- **MCP服务器模式**: `./aigoplay -mcp` - 提供MCP协议接口

## 技术架构

### 录制器 (browser/recorder.go)
```go
// 旁路录制方法
func (r *Recorder) StartStreamingToRTMP(ctx context.Context, rtmpURL string) error
```

**功能特点:**
- 使用Playwright控制浏览器
- FFmpeg进行屏幕捕获和编码
- 直接推流到RTMP服务器
- 支持实时状态检查

### RTMP服务器 (rtmp/server.go)
```go
// 纯Go RTMP服务器
type RTMPServer struct {
    config     *config.Config
    tcpServer  net.Listener
    httpServer *http.Server
    streams    map[string]*StreamInfo
    running    bool
    mutex      sync.RWMutex
}
```

**功能特点:**
- TCP监听端口1935接收RTMP流
- HTTP服务器端口8080提供HLS访问
- 流管理和状态监控
- 并发安全的流处理

### Web服务器 (web/server.go)
```go
// 旁路录制接口
func (ws *WebServer) startStreamingRecording(rtmpURL string) error
```

**API端点:**
- `POST /api/recording/start` - 开始旁路录制
- `POST /api/recording/stop` - 停止录制
- `GET /api/status` - 获取状态信息
- WebSocket实时状态推送

### MCP服务器 (mcp/server.go)
```go
// MCP方法支持
- start_recording: 开始旁路录制
- stop_recording: 停止录制
- get_status: 获取状态
- take_screenshot: 截图
```

## 使用方法

### 1. 命令行模式
```bash
# 基本使用
./aigoplay

# 指定参数
./aigoplay -url "https://youtube.com" -duration 300

# 无头模式
./aigoplay -headless
```

### 2. Web界面模式
```bash
# 启动Web界面
./aigoplay -web -port 8090

# 访问 http://localhost:8090
# 通过Web界面控制录制和推流
```

### 3. MCP服务器模式
```bash
# 启动MCP服务器
./aigoplay -mcp

# MCP服务器运行在端口8082
# 提供HTTP JSON API接口
```

## API使用示例

### Web API
```bash
# 开始旁路录制
curl -X POST http://localhost:8090/api/recording/start \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://youtube.com",
    "rtmp_url": "rtmp://localhost:1935/live/stream",
    "headless": false
  }'

# 获取状态
curl http://localhost:8090/api/status
```

### MCP API
```bash
# 开始录制
curl -X POST http://localhost:8082/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "method": "start_recording",
    "params": {
      "url": "https://youtube.com",
      "rtmp_url": "rtmp://localhost:1935/live/stream"
    }
  }'
```

## 流媒体访问

### RTMP推流地址
```
rtmp://localhost:1935/live/stream
```

### HLS播放地址
```
http://localhost:8080/hls/stream.m3u8
```

### 状态监控
```
http://localhost:8080/status
```

## 配置文件

```yaml
browser:
  url: "https://youtube.com"
  headless: false
  width: 1920
  height: 1080
  recording_path: "./recordings"
  wait_time: "5s"

rtmp:
  server_url: "rtmp://localhost:1935/live"
  stream_key: "stream"
  bitrate: 2000
  frame_rate: 30
  audio_bitrate: 128

video:
  format: "mp4"
  quality: "high"
  duration: 0
  output_path: "./output"
```

## 系统要求

- **FFmpeg**: 用于视频编码和推流
- **Chrome/Chromium**: Playwright自动下载
- **Go 1.19+**: 编译运行环境

## 优势特点

1. **低延迟**: 旁路录制避免文件IO，减少延迟
2. **高效率**: 直接推流，节省存储空间
3. **纯Go实现**: 无外部依赖，部署简单
4. **多模式支持**: 命令行、Web界面、MCP协议
5. **实时监控**: WebSocket实时状态推送
6. **跨平台**: 支持macOS和Linux

## 编译和运行

```bash
# 编译
go build -o aigoplay .

# 运行测试
go run test_streaming.go

# 启动服务
./aigoplay -web
```

项目现已完全实现旁路录制功能，支持实时浏览器内容推流到RTMP服务器，提供完整的Web界面和MCP协议支持。
