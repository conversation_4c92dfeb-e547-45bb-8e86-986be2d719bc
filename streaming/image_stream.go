package streaming

import (
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// ImageStreamer 纯Go图像流服务器
type ImageStreamer struct {
	port        int
	server      *http.Server
	clients     map[*websocket.Conn]bool
	clientMutex sync.RWMutex
	upgrader    websocket.Upgrader
	isRunning   bool
	mutex       sync.RWMutex
}

// NewImageStreamer 创建新的图像流服务器
func NewImageStreamer(port int) *ImageStreamer {
	return &ImageStreamer{
		port:    port,
		clients: make(map[*websocket.Conn]bool),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许所有来源
			},
		},
	}
}

// Start 启动图像流服务器
func (is *ImageStreamer) Start() error {
	is.mutex.Lock()
	defer is.mutex.Unlock()

	if is.isRunning {
		return fmt.Errorf("image streamer is already running")
	}

	mux := http.NewServeMux()
	
	// WebSocket端点用于实时图像流
	mux.HandleFunc("/stream", is.handleWebSocket)
	
	// HTTP端点用于获取当前帧
	mux.HandleFunc("/current", is.handleCurrentFrame)
	
	// 静态文件服务，提供简单的查看器
	mux.HandleFunc("/", is.handleViewer)

	is.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", is.port),
		Handler: mux,
	}

	is.isRunning = true
	log.Printf("Image streamer started on port %d", is.port)
	log.Printf("View stream at: http://localhost:%d", is.port)

	go func() {
		if err := is.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("Image streamer error: %v", err)
		}
	}()

	return nil
}

// Stop 停止图像流服务器
func (is *ImageStreamer) Stop() error {
	is.mutex.Lock()
	defer is.mutex.Unlock()

	if !is.isRunning {
		return nil
	}

	// 关闭所有WebSocket连接
	is.clientMutex.Lock()
	for conn := range is.clients {
		conn.Close()
	}
	is.clients = make(map[*websocket.Conn]bool)
	is.clientMutex.Unlock()

	// 关闭HTTP服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := is.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("failed to shutdown image streamer: %w", err)
	}

	is.isRunning = false
	log.Printf("Image streamer stopped")
	return nil
}

// IsRunning 检查服务器是否运行中
func (is *ImageStreamer) IsRunning() bool {
	is.mutex.RLock()
	defer is.mutex.RUnlock()
	return is.isRunning
}

// BroadcastFrame 广播新帧到所有连接的客户端
func (is *ImageStreamer) BroadcastFrame(frameData []byte) error {
	if !is.IsRunning() {
		return fmt.Errorf("image streamer is not running")
	}

	// 将图像数据编码为base64
	base64Data := base64.StdEncoding.EncodeToString(frameData)
	
	message := map[string]interface{}{
		"type":      "frame",
		"data":      base64Data,
		"timestamp": time.Now().Unix(),
	}

	is.clientMutex.RLock()
	defer is.clientMutex.RUnlock()

	// 广播到所有连接的客户端
	for conn := range is.clients {
		if err := conn.WriteJSON(message); err != nil {
			log.Printf("Error sending frame to client: %v", err)
			// 移除断开的连接
			go func(c *websocket.Conn) {
				is.clientMutex.Lock()
				delete(is.clients, c)
				is.clientMutex.Unlock()
				c.Close()
			}(conn)
		}
	}

	return nil
}

// handleWebSocket 处理WebSocket连接
func (is *ImageStreamer) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := is.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	is.clientMutex.Lock()
	is.clients[conn] = true
	clientCount := len(is.clients)
	is.clientMutex.Unlock()

	log.Printf("New WebSocket client connected. Total clients: %d", clientCount)

	// 发送欢迎消息
	welcomeMsg := map[string]interface{}{
		"type":    "welcome",
		"message": "Connected to image stream",
	}
	conn.WriteJSON(welcomeMsg)

	// 处理客户端消息（保持连接活跃）
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			log.Printf("WebSocket read error: %v", err)
			break
		}
	}

	// 清理连接
	is.clientMutex.Lock()
	delete(is.clients, conn)
	clientCount = len(is.clients)
	is.clientMutex.Unlock()

	conn.Close()
	log.Printf("WebSocket client disconnected. Total clients: %d", clientCount)
}

// handleCurrentFrame 处理当前帧请求
func (is *ImageStreamer) handleCurrentFrame(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"message": "Current frame endpoint - not implemented yet"}`))
}

// handleViewer 提供简单的HTML查看器
func (is *ImageStreamer) handleViewer(w http.ResponseWriter, r *http.Request) {
	html := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <title>Pure Go Image Stream Viewer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f0; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 20px; }
        .stream-container { text-align: center; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        #streamImage { max-width: 100%%; height: auto; border: 1px solid #ddd; }
        .status { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .info { margin-top: 20px; text-align: left; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Pure Go Image Stream Viewer</h1>
            <p>Real-time browser page streaming without FFmpeg dependencies</p>
        </div>
        
        <div class="stream-container">
            <img id="streamImage" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="Stream will appear here">
            <div id="status" class="status disconnected">Connecting...</div>
        </div>
        
        <div class="info">
            <h3>Features:</h3>
            <ul>
                <li>✅ Pure Go implementation - no FFmpeg dependency</li>
                <li>✅ Real-time WebSocket streaming</li>
                <li>✅ Browser page capture via Playwright</li>
                <li>✅ No system permissions required</li>
                <li>✅ Cross-platform compatibility</li>
            </ul>
        </div>
    </div>

    <script>
        const ws = new WebSocket('ws://localhost:%d/stream');
        const statusDiv = document.getElementById('status');
        const streamImage = document.getElementById('streamImage');
        
        ws.onopen = function() {
            statusDiv.textContent = 'Connected - Waiting for frames...';
            statusDiv.className = 'status connected';
        };
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            if (data.type === 'frame') {
                streamImage.src = 'data:image/png;base64,' + data.data;
                statusDiv.textContent = 'Streaming - Last frame: ' + new Date(data.timestamp * 1000).toLocaleTimeString();
            }
        };
        
        ws.onclose = function() {
            statusDiv.textContent = 'Disconnected';
            statusDiv.className = 'status disconnected';
        };
        
        ws.onerror = function(error) {
            statusDiv.textContent = 'Connection error';
            statusDiv.className = 'status disconnected';
        };
    </script>
</body>
</html>`, is.port)

	w.Header().Set("Content-Type", "text/html")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(html))
}

// GetClientCount 获取连接的客户端数量
func (is *ImageStreamer) GetClientCount() int {
	is.clientMutex.RLock()
	defer is.clientMutex.RUnlock()
	return len(is.clients)
}
