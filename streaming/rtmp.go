package streaming

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"sync"
	"time"

	"aigoplay/config"
)

// RTMPStreamer RTMP推流器
type RTMPStreamer struct {
	config    *config.Config
	cmd       *exec.Cmd
	isRunning bool
	mutex     sync.RWMutex
	cancel    context.CancelFunc
}

// NewRTMPStreamer 创建新的RTMP推流器
func NewRTMPStreamer(cfg *config.Config) *RTMPStreamer {
	return &RTMPStreamer{
		config:    cfg,
		isRunning: false,
	}
}

// StartStreaming 开始推流
func (s *RTMPStreamer) StartStreaming(ctx context.Context, inputSource string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("streaming is already running")
	}

	// 检查输入源是否存在
	if inputSource != "" {
		if _, err := os.Stat(inputSource); os.IsNotExist(err) {
			return fmt.Errorf("input source does not exist: %s", inputSource)
		}
	}

	// 创建带取消的上下文
	streamCtx, cancel := context.WithCancel(ctx)
	s.cancel = cancel

	// 构建RTMP推流URL
	rtmpURL := fmt.Sprintf("%s/%s", s.config.RTMP.ServerURL, s.config.RTMP.StreamKey)

	// 构建FFmpeg命令
	var args []string

	if inputSource != "" {
		// 从文件推流
		args = []string{
			"-re",                    // 以原始帧率读取输入
			"-i", inputSource,        // 输入文件
			"-c:v", "libx264",        // 视频编码器
			"-preset", "fast",        // 编码预设
			"-b:v", fmt.Sprintf("%dk", s.config.RTMP.Bitrate), // 视频比特率
			"-maxrate", fmt.Sprintf("%dk", s.config.RTMP.Bitrate),
			"-bufsize", fmt.Sprintf("%dk", s.config.RTMP.Bitrate*2),
			"-r", fmt.Sprintf("%d", s.config.RTMP.FrameRate), // 帧率
			"-g", fmt.Sprintf("%d", s.config.RTMP.FrameRate*2), // GOP大小
			"-c:a", "aac",            // 音频编码器
			"-b:a", fmt.Sprintf("%dk", s.config.RTMP.AudioBitrate), // 音频比特率
			"-ar", "44100",           // 音频采样率
			"-f", "flv",              // 输出格式
			rtmpURL,                  // RTMP URL
		}
	} else {
		// 从桌面捕获推流（macOS）
		args = []string{
			"-f", "avfoundation",     // macOS 输入格式
			"-i", "1:0",              // 屏幕:音频设备
			"-c:v", "libx264",        // 视频编码器
			"-preset", "fast",        // 编码预设
			"-b:v", fmt.Sprintf("%dk", s.config.RTMP.Bitrate), // 视频比特率
			"-maxrate", fmt.Sprintf("%dk", s.config.RTMP.Bitrate),
			"-bufsize", fmt.Sprintf("%dk", s.config.RTMP.Bitrate*2),
			"-r", fmt.Sprintf("%d", s.config.RTMP.FrameRate), // 帧率
			"-g", fmt.Sprintf("%d", s.config.RTMP.FrameRate*2), // GOP大小
			"-c:a", "aac",            // 音频编码器
			"-b:a", fmt.Sprintf("%dk", s.config.RTMP.AudioBitrate), // 音频比特率
			"-ar", "44100",           // 音频采样率
			"-f", "flv",              // 输出格式
			rtmpURL,                  // RTMP URL
		}
	}

	// 创建FFmpeg命令
	s.cmd = exec.CommandContext(streamCtx, "ffmpeg", args...)

	// 设置输出
	s.cmd.Stdout = os.Stdout
	s.cmd.Stderr = os.Stderr

	log.Printf("Starting RTMP stream to: %s", rtmpURL)
	log.Printf("FFmpeg command: ffmpeg %v", args)

	// 启动命令
	if err := s.cmd.Start(); err != nil {
		s.cancel()
		return fmt.Errorf("failed to start FFmpeg: %w", err)
	}

	s.isRunning = true

	// 在goroutine中等待命令完成
	go func() {
		err := s.cmd.Wait()
		s.mutex.Lock()
		s.isRunning = false
		s.mutex.Unlock()

		if err != nil && streamCtx.Err() == nil {
			log.Printf("FFmpeg process ended with error: %v", err)
		} else {
			log.Printf("FFmpeg process ended normally")
		}
	}()

	log.Printf("RTMP streaming started successfully")
	return nil
}

// StopStreaming 停止推流
func (s *RTMPStreamer) StopStreaming() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return fmt.Errorf("streaming is not running")
	}

	log.Printf("Stopping RTMP stream...")

	// 取消上下文
	if s.cancel != nil {
		s.cancel()
	}

	// 等待进程结束
	if s.cmd != nil && s.cmd.Process != nil {
		// 给进程一些时间优雅退出
		time.Sleep(2 * time.Second)

		// 如果还在运行，强制终止
		if s.isRunning {
			if err := s.cmd.Process.Kill(); err != nil {
				log.Printf("Warning: Failed to kill FFmpeg process: %v", err)
			}
		}
	}

	s.isRunning = false
	log.Printf("RTMP streaming stopped")

	return nil
}

// IsRunning 检查是否正在推流
func (s *RTMPStreamer) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// StreamFromFile 从文件推流
func (s *RTMPStreamer) StreamFromFile(ctx context.Context, filePath string) error {
	if !filepath.IsAbs(filePath) {
		abs, err := filepath.Abs(filePath)
		if err != nil {
			return fmt.Errorf("failed to get absolute path: %w", err)
		}
		filePath = abs
	}

	return s.StartStreaming(ctx, filePath)
}

// StreamDesktop 推流桌面
func (s *RTMPStreamer) StreamDesktop(ctx context.Context) error {
	return s.StartStreaming(ctx, "")
}

// GetStreamURL 获取推流URL
func (s *RTMPStreamer) GetStreamURL() string {
	return fmt.Sprintf("%s/%s", s.config.RTMP.ServerURL, s.config.RTMP.StreamKey)
}

// CheckFFmpeg 检查FFmpeg是否可用
func CheckFFmpeg() error {
	cmd := exec.Command("ffmpeg", "-version")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("FFmpeg not found or not working: %w", err)
	}
	return nil
}

// StreamingStats 推流统计信息
type StreamingStats struct {
	Duration    time.Duration `json:"duration"`
	Bitrate     int           `json:"bitrate"`
	FrameRate   int           `json:"frame_rate"`
	IsRunning   bool          `json:"is_running"`
	StreamURL   string        `json:"stream_url"`
	StartTime   time.Time     `json:"start_time"`
}

// GetStats 获取推流统计信息
func (s *RTMPStreamer) GetStats() StreamingStats {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := StreamingStats{
		Bitrate:   s.config.RTMP.Bitrate,
		FrameRate: s.config.RTMP.FrameRate,
		IsRunning: s.isRunning,
		StreamURL: s.GetStreamURL(),
	}

	return stats
}
