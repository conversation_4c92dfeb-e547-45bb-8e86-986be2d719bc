<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active { background-color: #28a745; }
        .status-inactive { background-color: #dc3545; }
        .video-container {
            position: relative;
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }
        .video-player {
            width: 100%;
            height: 100%;
        }
        .control-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .log-container {
            height: 200px;
            overflow-y: auto;
            background: #1e1e1e;
            color: #fff;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-video"></i>
                    Browser Recording & RTMP Streaming
                </h1>
            </div>
        </div>

        <!-- Status Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="control-panel">
                    <h5><i class="fas fa-info-circle"></i> System Status</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator" id="rtmp-status"></span>
                                <span>RTMP Server</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator" id="recording-status"></span>
                                <span>Recording</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator" id="streaming-status"></span>
                                <span>Streaming</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">
                                URL: <span id="current-url">-</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Control Panel -->
            <div class="col-md-4">
                <div class="control-panel">
                    <h5><i class="fas fa-cogs"></i> Controls</h5>
                    
                    <!-- RTMP Server Controls -->
                    <div class="mb-3">
                        <h6>RTMP Server</h6>
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-success" id="start-rtmp">
                                <i class="fas fa-play"></i> Start
                            </button>
                            <button type="button" class="btn btn-danger" id="stop-rtmp">
                                <i class="fas fa-stop"></i> Stop
                            </button>
                        </div>
                    </div>

                    <!-- Recording Controls -->
                    <div class="mb-3">
                        <h6>Recording</h6>
                        <div class="mb-2">
                            <label for="url-input" class="form-label">URL to Record</label>
                            <input type="url" class="form-control" id="url-input" placeholder="https://example.com">
                        </div>
                        <div class="mb-2">
                            <label for="duration-input" class="form-label">Duration (seconds, 0 = unlimited)</label>
                            <input type="number" class="form-control" id="duration-input" value="0" min="0">
                        </div>
                        <div class="mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="headless-check">
                                <label class="form-check-label" for="headless-check">
                                    Headless Mode
                                </label>
                            </div>
                        </div>
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-primary" id="start-recording">
                                <i class="fas fa-record-vinyl"></i> Start Recording
                            </button>
                            <button type="button" class="btn btn-warning" id="stop-recording">
                                <i class="fas fa-stop"></i> Stop
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Configuration Panel -->
                <div class="control-panel">
                    <h5><i class="fas fa-sliders-h"></i> Configuration</h5>
                    <button type="button" class="btn btn-info w-100" id="show-config">
                        <i class="fas fa-edit"></i> Edit Configuration
                    </button>
                </div>

                <!-- Logs -->
                <div class="control-panel">
                    <h5><i class="fas fa-terminal"></i> Logs</h5>
                    <div class="log-container" id="log-container">
                        <div class="text-muted">Waiting for logs...</div>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-secondary mt-2" id="clear-logs">
                        <i class="fas fa-trash"></i> Clear Logs
                    </button>
                </div>
            </div>

            <!-- Video Player -->
            <div class="col-md-8">
                <div class="control-panel">
                    <h5><i class="fas fa-play-circle"></i> Live Stream</h5>
                    <div class="video-container">
                        <video class="video-player" id="video-player" controls autoplay muted>
                            <source src="http://localhost:8080/hls/stream.m3u8" type="application/x-mpegURL">
                            Your browser does not support HLS playback.
                        </video>
                        <div class="position-absolute top-50 start-50 translate-middle text-white" id="video-placeholder">
                            <div class="text-center">
                                <i class="fas fa-video fa-3x mb-3"></i>
                                <p>Start RTMP server and recording to see live stream</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Stream URL: <code>rtmp://localhost:1935/live/stream</code><br>
                            HLS URL: <code>http://localhost:8080/hls/stream.m3u8</code>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Modal -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Configuration</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <textarea class="form-control" id="config-editor" rows="20" style="font-family: monospace;"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-config">Save Configuration</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="/static/app.js"></script>
</body>
</html>
