package web

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"path/filepath"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"aigoplay/browser"
	"aigoplay/config"
	"aigoplay/rtmp"
	"aigoplay/streaming"
)

type WebServer struct {
	config          *config.Config
	router          *gin.Engine
	server          *http.Server
	rtmpServer      *rtmp.RTMPServer
	rtmpMutex       sync.RWMutex
	recorder        *browser.Recorder
	streamer        *streaming.RTMPStreamer
	wsUpgrader      websocket.Upgrader
	wsClients       map[*websocket.Conn]bool
	wsMutex         sync.RWMutex
	recordingCtx    context.Context
	recordingCancel context.CancelFunc
}

type WebResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type RecordingStatus struct {
	Recording   bool   `json:"recording"`
	Streaming   bool   `json:"streaming"`
	RTMPRunning bool   `json:"rtmp_running"`
	URL         string `json:"url"`
	Duration    int    `json:"duration"`
}

func NewWebServer(cfg *config.Config) *WebServer {
	gin.SetMode(gin.ReleaseMode)
	router := gin.Default()

	ws := &WebServer{
		config:    cfg,
		router:    router,
		wsClients: make(map[*websocket.Conn]bool),
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for development
			},
		},
	}

	ws.setupRoutes()
	return ws
}

func (ws *WebServer) setupRoutes() {
	// Serve static files
	ws.router.Static("/static", "./web/static")
	ws.router.LoadHTMLGlob("web/templates/*")

	// Main page
	ws.router.GET("/", ws.handleIndex)

	// API routes
	api := ws.router.Group("/api")
	{
		api.GET("/status", ws.handleStatus)
		api.POST("/rtmp/start", ws.handleStartRTMP)
		api.POST("/rtmp/stop", ws.handleStopRTMP)
		api.POST("/recording/start", ws.handleStartRecording)
		api.POST("/recording/stop", ws.handleStopRecording)
		api.GET("/config", ws.handleGetConfig)
		api.POST("/config", ws.handleUpdateConfig)
	}

	// WebSocket endpoint
	ws.router.GET("/ws", ws.handleWebSocket)
}

func (ws *WebServer) handleIndex(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title": "Browser Recording & RTMP Streaming",
	})
}

func (ws *WebServer) handleStatus(c *gin.Context) {
	rtmpRunning := false
	if ws.rtmpServer != nil {
		rtmpRunning = ws.rtmpServer.IsRunning()
	}

	recording := ws.recorder != nil && ws.recordingCtx != nil
	streaming := ws.streamer != nil

	status := RecordingStatus{
		Recording:   recording,
		Streaming:   streaming,
		RTMPRunning: rtmpRunning,
		URL:         ws.config.Browser.URL,
		Duration:    ws.config.Video.Duration,
	}

	c.JSON(http.StatusOK, WebResponse{
		Success: true,
		Data:    status,
	})
}

func (ws *WebServer) handleStartRTMP(c *gin.Context) {
	ws.rtmpMutex.Lock()
	defer ws.rtmpMutex.Unlock()

	if ws.rtmpServer != nil && ws.rtmpServer.IsRunning() {
		c.JSON(http.StatusOK, WebResponse{
			Success: false,
			Message: "RTMP server is already running",
		})
		return
	}

	if err := ws.startRTMPServer(); err != nil {
		c.JSON(http.StatusInternalServerError, WebResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to start RTMP server: %v", err),
		})
		return
	}

	ws.broadcastStatus()

	c.JSON(http.StatusOK, WebResponse{
		Success: true,
		Message: "RTMP server started successfully",
	})
}

func (ws *WebServer) handleStopRTMP(c *gin.Context) {
	ws.rtmpMutex.Lock()
	defer ws.rtmpMutex.Unlock()

	if ws.rtmpServer == nil || !ws.rtmpServer.IsRunning() {
		c.JSON(http.StatusOK, WebResponse{
			Success: false,
			Message: "RTMP server is not running",
		})
		return
	}

	ws.stopRTMPServer()
	ws.broadcastStatus()

	c.JSON(http.StatusOK, WebResponse{
		Success: true,
		Message: "RTMP server stopped successfully",
	})
}

func (ws *WebServer) handleStartRecording(c *gin.Context) {
	var req struct {
		URL      string `json:"url"`
		Duration int    `json:"duration"`
		Headless bool   `json:"headless"`
		RTMPURL  string `json:"rtmp_url"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, WebResponse{
			Success: false,
			Message: "Invalid request format",
		})
		return
	}

	// Update config if provided
	if req.URL != "" {
		ws.config.Browser.URL = req.URL
	}
	if req.Duration > 0 {
		ws.config.Video.Duration = req.Duration
	}
	ws.config.Browser.Headless = req.Headless

	// 构建RTMP URL
	rtmpURL := req.RTMPURL
	if rtmpURL == "" {
		// 使用默认的本地RTMP服务器
		rtmpURL = "rtmp://localhost:1935/live/stream"
	}

	if err := ws.startStreamingRecording(rtmpURL); err != nil {
		c.JSON(http.StatusInternalServerError, WebResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to start streaming recording: %v", err),
		})
		return
	}

	ws.broadcastStatus()

	c.JSON(http.StatusOK, WebResponse{
		Success: true,
		Message: "Streaming recording started successfully",
		Data: map[string]interface{}{
			"rtmp_url": rtmpURL,
		},
	})
}

func (ws *WebServer) handleStopRecording(c *gin.Context) {
	if ws.recordingCancel != nil {
		ws.recordingCancel()
		ws.recordingCancel = nil
		ws.recordingCtx = nil
	}

	if ws.recorder != nil {
		ws.recorder.StopRecording()
		ws.recorder.Cleanup()
		ws.recorder = nil
	}

	if ws.streamer != nil {
		ws.streamer.StopStreaming()
		ws.streamer = nil
	}

	ws.broadcastStatus()

	c.JSON(http.StatusOK, WebResponse{
		Success: true,
		Message: "Recording stopped successfully",
	})
}

func (ws *WebServer) handleGetConfig(c *gin.Context) {
	c.JSON(http.StatusOK, WebResponse{
		Success: true,
		Data:    ws.config,
	})
}

func (ws *WebServer) handleUpdateConfig(c *gin.Context) {
	var newConfig config.Config
	if err := c.ShouldBindJSON(&newConfig); err != nil {
		c.JSON(http.StatusBadRequest, WebResponse{
			Success: false,
			Message: "Invalid configuration format",
		})
		return
	}

	// Validate the new configuration
	if err := newConfig.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, WebResponse{
			Success: false,
			Message: fmt.Sprintf("Invalid configuration: %v", err),
		})
		return
	}

	ws.config = &newConfig

	c.JSON(http.StatusOK, WebResponse{
		Success: true,
		Message: "Configuration updated successfully",
	})
}

func (ws *WebServer) Start(port int) error {
	ws.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: ws.router,
	}

	log.Printf("Starting web server on port %d", port)
	return ws.server.ListenAndServe()
}

func (ws *WebServer) Stop() error {
	if ws.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		return ws.server.Shutdown(ctx)
	}
	return nil
}

// WebSocket handling
func (ws *WebServer) handleWebSocket(c *gin.Context) {
	conn, err := ws.wsUpgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	ws.wsMutex.Lock()
	ws.wsClients[conn] = true
	ws.wsMutex.Unlock()

	defer func() {
		ws.wsMutex.Lock()
		delete(ws.wsClients, conn)
		ws.wsMutex.Unlock()
	}()

	// Send initial status
	ws.sendStatusToClient(conn)

	// Keep connection alive and handle messages
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			break
		}
	}
}

func (ws *WebServer) sendStatusToClient(conn *websocket.Conn) {
	rtmpRunning := false
	if ws.rtmpServer != nil {
		rtmpRunning = ws.rtmpServer.IsRunning()
	}

	recording := ws.recorder != nil && ws.recordingCtx != nil
	streaming := ws.streamer != nil

	status := RecordingStatus{
		Recording:   recording,
		Streaming:   streaming,
		RTMPRunning: rtmpRunning,
		URL:         ws.config.Browser.URL,
		Duration:    ws.config.Video.Duration,
	}

	message := map[string]interface{}{
		"type": "status",
		"data": status,
	}

	conn.WriteJSON(message)
}

func (ws *WebServer) broadcastStatus() {
	ws.wsMutex.RLock()
	defer ws.wsMutex.RUnlock()

	for conn := range ws.wsClients {
		ws.sendStatusToClient(conn)
	}
}

// RTMP Server management
func (ws *WebServer) startRTMPServer() error {
	// Create RTMP server if not exists
	if ws.rtmpServer == nil {
		ws.rtmpServer = rtmp.NewRTMPServer(ws.config)
	}

	// Start the RTMP server
	if err := ws.rtmpServer.Start(); err != nil {
		return fmt.Errorf("failed to start RTMP server: %v", err)
	}

	return nil
}

func (ws *WebServer) stopRTMPServer() {
	if ws.rtmpServer != nil {
		ws.rtmpServer.Stop()
		log.Println("RTMP server stopped")
	}
}

// Recording management
func (ws *WebServer) startRecording() error {
	// Stop any existing recording
	if ws.recordingCancel != nil {
		ws.recordingCancel()
	}

	// Create new context for recording
	ws.recordingCtx, ws.recordingCancel = context.WithCancel(context.Background())

	// Initialize browser recorder
	ws.recorder = browser.NewRecorder(ws.config)

	// Initialize RTMP streamer
	ws.streamer = streaming.NewRTMPStreamer(ws.config)

	// Start recording in a goroutine
	go func() {
		defer func() {
			if ws.recorder != nil {
				ws.recorder.StopRecording()
				ws.recorder.Cleanup()
			}
			if ws.streamer != nil {
				ws.streamer.StopStreaming()
			}
		}()

		// Initialize and start browser recording
		if err := ws.recorder.Initialize(ws.recordingCtx); err != nil {
			log.Printf("Failed to initialize recorder: %v", err)
			return
		}

		if err := ws.recorder.StartRecording(ws.recordingCtx); err != nil {
			log.Printf("Recording failed: %v", err)
			return
		}

		// Wait for recording to generate some content
		time.Sleep(5 * time.Second)

		// Start RTMP streaming
		recordingPath := filepath.Join(ws.config.Browser.RecordingPath, "*.webm")
		if err := ws.streamer.StreamFromFile(ws.recordingCtx, recordingPath); err != nil {
			log.Printf("Streaming failed: %v", err)
			return
		}

		log.Println("Recording and streaming started successfully")
	}()

	return nil
}
