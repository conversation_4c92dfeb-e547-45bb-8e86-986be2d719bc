class StreamingApp {
    constructor() {
        this.ws = null;
        this.hls = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        
        this.initializeWebSocket();
        this.initializeEventListeners();
        this.initializeVideoPlayer();
        this.loadStatus();
    }

    initializeWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
            this.addLog('WebSocket connected', 'success');
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.addLog('WebSocket disconnected', 'warning');
            this.attemptReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.addLog('WebSocket error', 'error');
        };
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            this.addLog(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`, 'info');
            setTimeout(() => {
                this.initializeWebSocket();
            }, 2000 * this.reconnectAttempts);
        } else {
            this.addLog('Max reconnection attempts reached', 'error');
        }
    }

    handleWebSocketMessage(data) {
        if (data.type === 'status') {
            this.updateStatus(data.data);
        }
    }

    initializeEventListeners() {
        // RTMP Server controls
        document.getElementById('start-rtmp').addEventListener('click', () => {
            this.startRTMPServer();
        });
        
        document.getElementById('stop-rtmp').addEventListener('click', () => {
            this.stopRTMPServer();
        });
        
        // Recording controls
        document.getElementById('start-recording').addEventListener('click', () => {
            this.startRecording();
        });
        
        document.getElementById('stop-recording').addEventListener('click', () => {
            this.stopRecording();
        });
        
        // Configuration
        document.getElementById('show-config').addEventListener('click', () => {
            this.showConfiguration();
        });
        
        document.getElementById('save-config').addEventListener('click', () => {
            this.saveConfiguration();
        });
        
        // Logs
        document.getElementById('clear-logs').addEventListener('click', () => {
            this.clearLogs();
        });
    }

    initializeVideoPlayer() {
        const video = document.getElementById('video-player');
        const hlsUrl = 'http://localhost:8080/hls/stream.m3u8';
        
        if (Hls.isSupported()) {
            this.hls = new Hls({
                enableWorker: false,
                lowLatencyMode: true,
                backBufferLength: 90
            });
            
            this.hls.loadSource(hlsUrl);
            this.hls.attachMedia(video);
            
            this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                console.log('HLS manifest parsed');
                this.hideVideoPlaceholder();
            });
            
            this.hls.on(Hls.Events.ERROR, (event, data) => {
                console.error('HLS error:', data);
                if (data.fatal) {
                    this.showVideoPlaceholder();
                    this.addLog(`HLS error: ${data.details}`, 'error');
                }
            });
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari native HLS support
            video.src = hlsUrl;
        } else {
            this.addLog('HLS not supported in this browser', 'error');
        }
    }

    showVideoPlaceholder() {
        document.getElementById('video-placeholder').style.display = 'block';
    }

    hideVideoPlaceholder() {
        document.getElementById('video-placeholder').style.display = 'none';
    }

    async loadStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            if (data.success) {
                this.updateStatus(data.data);
            }
        } catch (error) {
            console.error('Failed to load status:', error);
            this.addLog('Failed to load status', 'error');
        }
    }

    updateStatus(status) {
        // Update status indicators
        this.updateStatusIndicator('rtmp-status', status.rtmp_running);
        this.updateStatusIndicator('recording-status', status.recording);
        this.updateStatusIndicator('streaming-status', status.streaming);
        
        // Update current URL
        document.getElementById('current-url').textContent = status.url || '-';
        
        // Update form fields
        document.getElementById('url-input').value = status.url || '';
        document.getElementById('duration-input').value = status.duration || 0;
        
        // Update button states
        this.updateButtonStates(status);
    }

    updateStatusIndicator(elementId, isActive) {
        const element = document.getElementById(elementId);
        element.className = `status-indicator ${isActive ? 'status-active' : 'status-inactive'}`;
    }

    updateButtonStates(status) {
        document.getElementById('start-rtmp').disabled = status.rtmp_running;
        document.getElementById('stop-rtmp').disabled = !status.rtmp_running;
        document.getElementById('start-recording').disabled = status.recording;
        document.getElementById('stop-recording').disabled = !status.recording;
    }

    async startRTMPServer() {
        try {
            this.addLog('Starting RTMP server...', 'info');
            const response = await fetch('/api/rtmp/start', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.addLog('RTMP server started successfully', 'success');
            } else {
                this.addLog(`Failed to start RTMP server: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error starting RTMP server:', error);
            this.addLog('Error starting RTMP server', 'error');
        }
    }

    async stopRTMPServer() {
        try {
            this.addLog('Stopping RTMP server...', 'info');
            const response = await fetch('/api/rtmp/stop', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.addLog('RTMP server stopped successfully', 'success');
                this.showVideoPlaceholder();
            } else {
                this.addLog(`Failed to stop RTMP server: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error stopping RTMP server:', error);
            this.addLog('Error stopping RTMP server', 'error');
        }
    }

    async startRecording() {
        try {
            const url = document.getElementById('url-input').value;
            const duration = parseInt(document.getElementById('duration-input').value) || 0;
            const headless = document.getElementById('headless-check').checked;
            
            if (!url) {
                this.addLog('Please enter a URL to record', 'warning');
                return;
            }
            
            this.addLog('Starting recording...', 'info');
            
            const response = await fetch('/api/recording/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    url: url,
                    duration: duration,
                    headless: headless
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.addLog('Recording started successfully', 'success');
            } else {
                this.addLog(`Failed to start recording: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error starting recording:', error);
            this.addLog('Error starting recording', 'error');
        }
    }

    async stopRecording() {
        try {
            this.addLog('Stopping recording...', 'info');
            const response = await fetch('/api/recording/stop', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.addLog('Recording stopped successfully', 'success');
            } else {
                this.addLog(`Failed to stop recording: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error stopping recording:', error);
            this.addLog('Error stopping recording', 'error');
        }
    }

    async showConfiguration() {
        try {
            const response = await fetch('/api/config');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('config-editor').value = JSON.stringify(data.data, null, 2);
                const modal = new bootstrap.Modal(document.getElementById('configModal'));
                modal.show();
            } else {
                this.addLog('Failed to load configuration', 'error');
            }
        } catch (error) {
            console.error('Error loading configuration:', error);
            this.addLog('Error loading configuration', 'error');
        }
    }

    async saveConfiguration() {
        try {
            const configText = document.getElementById('config-editor').value;
            const config = JSON.parse(configText);
            
            const response = await fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.addLog('Configuration saved successfully', 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
                modal.hide();
                this.loadStatus(); // Reload status with new config
            } else {
                this.addLog(`Failed to save configuration: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Error saving configuration:', error);
            this.addLog('Error saving configuration', 'error');
        }
    }

    addLog(message, type = 'info') {
        const logContainer = document.getElementById('log-container');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        
        let color = '#fff';
        switch (type) {
            case 'success': color = '#28a745'; break;
            case 'error': color = '#dc3545'; break;
            case 'warning': color = '#ffc107'; break;
            case 'info': color = '#17a2b8'; break;
        }
        
        logEntry.innerHTML = `<span style="color: #888">[${timestamp}]</span> <span style="color: ${color}">${message}</span>`;
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    clearLogs() {
        document.getElementById('log-container').innerHTML = '<div class="text-muted">Logs cleared</div>';
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new StreamingApp();
});
