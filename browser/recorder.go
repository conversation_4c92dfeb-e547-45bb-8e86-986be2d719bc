package browser

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"aigoplay/config"

	"github.com/playwright-community/playwright-go"
)

// Recorder 浏览器录制器
type Recorder struct {
	config    *config.Config
	pw        *playwright.Playwright
	browser   playwright.Browser
	page      playwright.Page
	context   playwright.BrowserContext
	isRunning bool
}

// NewRecorder 创建新的录制器
func NewRecorder(cfg *config.Config) *Recorder {
	return &Recorder{
		config:    cfg,
		isRunning: false,
	}
}

// Initialize 初始化浏览器
func (r *Recorder) Initialize(ctx context.Context) error {
	// 安装 playwright 浏览器（如果需要）
	err := playwright.Install()
	if err != nil {
		log.Printf("Warning: Failed to install playwright browsers: %v", err)
	}

	// 启动 playwright
	pw, err := playwright.Run()
	if err != nil {
		return fmt.Errorf("failed to start playwright: %w", err)
	}
	r.pw = pw

	// 启动浏览器
	browser, err := pw.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
		Headless: playwright.Bool(r.config.Browser.Headless),
		Args: []string{
			"--no-sandbox",
			"--disable-dev-shm-usage",
			"--disable-gpu",
			"--enable-webrtc-capture-all-desktop-media",
			"--auto-select-desktop-capture-source=Entire screen",
		},
	})
	if err != nil {
		return fmt.Errorf("failed to launch browser: %w", err)
	}
	r.browser = browser

	// 创建浏览器上下文
	contextOptions := playwright.BrowserNewContextOptions{
		ViewportSize: &playwright.Size{
			Width:  r.config.Browser.ViewportWidth,
			Height: r.config.Browser.ViewportHeight,
		},
		RecordVideo: &playwright.BrowserNewContextOptionsRecordVideo{
			Dir: r.config.Browser.RecordingPath,
		},
	}

	if r.config.Browser.UserAgent != "" {
		contextOptions.UserAgent = playwright.String(r.config.Browser.UserAgent)
	}

	browserContext, err := browser.NewContext(contextOptions)
	if err != nil {
		return fmt.Errorf("failed to create browser context: %w", err)
	}
	r.context = browserContext

	// 创建页面
	page, err := browserContext.NewPage()
	if err != nil {
		return fmt.Errorf("failed to create page: %w", err)
	}
	r.page = page

	return nil
}

// StartRecording 开始录制
func (r *Recorder) StartRecording(ctx context.Context) error {
	if r.isRunning {
		return fmt.Errorf("recording is already running")
	}

	// 确保录制目录存在
	if err := os.MkdirAll(r.config.Browser.RecordingPath, 0755); err != nil {
		return fmt.Errorf("failed to create recording directory: %w", err)
	}

	log.Printf("Opening URL: %s", r.config.Browser.URL)

	// 导航到指定URL
	_, err := r.page.Goto(r.config.Browser.URL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateNetworkidle,
		Timeout:   playwright.Float(30000), // 30秒超时
	})
	if err != nil {
		return fmt.Errorf("failed to navigate to URL: %w", err)
	}

	// 等待页面加载
	log.Printf("Waiting for page to load...")
	time.Sleep(r.config.Browser.WaitTime)

	r.isRunning = true
	log.Printf("Recording started successfully")

	return nil
}

// StopRecording 停止录制
func (r *Recorder) StopRecording() error {
	if !r.isRunning {
		return fmt.Errorf("recording is not running")
	}

	log.Printf("Stopping recording...")

	// 关闭页面和上下文以完成视频录制
	if r.page != nil {
		if err := r.page.Close(); err != nil {
			log.Printf("Warning: Failed to close page: %v", err)
		}
	}

	if r.context != nil {
		if err := r.context.Close(); err != nil {
			log.Printf("Warning: Failed to close context: %v", err)
		}
	}

	r.isRunning = false
	log.Printf("Recording stopped")

	return nil
}

// Cleanup 清理资源
func (r *Recorder) Cleanup() error {
	if r.isRunning {
		if err := r.StopRecording(); err != nil {
			log.Printf("Warning: Failed to stop recording: %v", err)
		}
	}

	if r.browser != nil {
		if err := r.browser.Close(); err != nil {
			log.Printf("Warning: Failed to close browser: %v", err)
		}
	}

	if r.pw != nil {
		if err := r.pw.Stop(); err != nil {
			log.Printf("Warning: Failed to stop playwright: %v", err)
		}
	}

	return nil
}

// GetRecordedVideoPath 获取录制的视频文件路径
func (r *Recorder) GetRecordedVideoPath() (string, error) {
	if r.page == nil {
		return "", fmt.Errorf("page not initialized")
	}

	// 获取视频路径
	videoPath, err := r.page.Video().Path()
	if err != nil {
		return "", fmt.Errorf("failed to get video path: %w", err)
	}

	return videoPath, nil
}

// TakeScreenshot 截图
func (r *Recorder) TakeScreenshot(filename string) error {
	if r.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 确保输出目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create screenshot directory: %w", err)
	}

	_, err := r.page.Screenshot(playwright.PageScreenshotOptions{
		Path: playwright.String(filename),
		Type: playwright.ScreenshotTypePng,
	})
	if err != nil {
		return fmt.Errorf("failed to take screenshot: %w", err)
	}

	log.Printf("Screenshot saved to: %s", filename)
	return nil
}

// IsRunning 检查是否正在录制
func (r *Recorder) IsRunning() bool {
	return r.isRunning
}

// ExecuteScript 在页面中执行JavaScript
func (r *Recorder) ExecuteScript(script string) (interface{}, error) {
	if r.page == nil {
		return nil, fmt.Errorf("page not initialized")
	}

	result, err := r.page.Evaluate(script)
	if err != nil {
		return nil, fmt.Errorf("failed to execute script: %w", err)
	}

	return result, nil
}

// WaitForElement 等待元素出现
func (r *Recorder) WaitForElement(selector string, timeout time.Duration) error {
	if r.page == nil {
		return fmt.Errorf("page not initialized")
	}

	_, err := r.page.WaitForSelector(selector, playwright.PageWaitForSelectorOptions{
		Timeout: playwright.Float(float64(timeout.Milliseconds())),
	})
	if err != nil {
		return fmt.Errorf("failed to wait for element %s: %w", selector, err)
	}

	return nil
}
