package browser

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"aigoplay/config"
	"aigoplay/streaming"

	"github.com/playwright-community/playwright-go"
)

// Recorder 浏览器录制器
type Recorder struct {
	config        *config.Config
	pw            *playwright.Playwright
	browser       playwright.Browser
	page          playwright.Page
	context       playwright.BrowserContext
	isRunning     bool
	isStreaming   bool
	streamURL     string
	imageStreamer *streaming.ImageStreamer
	streamCtx     context.Context
	streamCancel  context.CancelFunc
	streamMutex   sync.RWMutex
	frameRate     int
	captureWidth  int
	captureHeight int
}

// NewRecorder 创建新的录制器
func NewRecorder(cfg *config.Config) *Recorder {
	return &Recorder{
		config:        cfg,
		isRunning:     false,
		isStreaming:   false,
		frameRate:     cfg.RTMP.FrameRate,
		captureWidth:  cfg.Browser.Width,
		captureHeight: cfg.Browser.Height,
	}
}

// Initialize 初始化浏览器
func (r *Recorder) Initialize(ctx context.Context) error {
	// 安装 playwright 浏览器（如果需要）
	err := playwright.Install()
	if err != nil {
		log.Printf("Warning: Failed to install playwright browsers: %v", err)
	}

	// 启动 playwright
	pw, err := playwright.Run()
	if err != nil {
		return fmt.Errorf("failed to start playwright: %w", err)
	}
	r.pw = pw

	// 启动浏览器
	browser, err := pw.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
		Headless: playwright.Bool(r.config.Browser.Headless),
		Args: []string{
			"--no-sandbox",
			"--disable-dev-shm-usage",
			"--disable-gpu",
			"--enable-webrtc-capture-all-desktop-media",
			"--auto-select-desktop-capture-source=Entire screen",
		},
	})
	if err != nil {
		return fmt.Errorf("failed to launch browser: %w", err)
	}
	r.browser = browser

	// 创建浏览器上下文
	contextOptions := playwright.BrowserNewContextOptions{
		Viewport: &playwright.Size{
			Width:  r.config.Browser.ViewportWidth,
			Height: r.config.Browser.ViewportHeight,
		},
		// 不使用Playwright的视频录制功能，我们使用纯Go屏幕捕获
	}

	if r.config.Browser.UserAgent != "" {
		contextOptions.UserAgent = playwright.String(r.config.Browser.UserAgent)
	}

	browserContext, err := browser.NewContext(contextOptions)
	if err != nil {
		return fmt.Errorf("failed to create browser context: %w", err)
	}
	r.context = browserContext

	// 创建页面
	page, err := browserContext.NewPage()
	if err != nil {
		return fmt.Errorf("failed to create page: %w", err)
	}
	r.page = page

	return nil
}

// StartRecording 开始录制
func (r *Recorder) StartRecording(ctx context.Context) error {
	if r.isRunning {
		return fmt.Errorf("recording is already running")
	}

	// 确保录制目录存在
	if err := os.MkdirAll(r.config.Browser.RecordingPath, 0755); err != nil {
		return fmt.Errorf("failed to create recording directory: %w", err)
	}

	log.Printf("Opening URL: %s", r.config.Browser.URL)

	// 导航到指定URL
	_, err := r.page.Goto(r.config.Browser.URL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateNetworkidle,
		Timeout:   playwright.Float(30000), // 30秒超时
	})
	if err != nil {
		return fmt.Errorf("failed to navigate to URL: %w", err)
	}

	// 等待页面加载
	log.Printf("Waiting for page to load...")
	time.Sleep(r.config.Browser.WaitTime)

	r.isRunning = true
	log.Printf("Recording started successfully")

	return nil
}

// StartStreamingToRTMP 开始旁路录制并推流到图像流服务器 (纯Go实现)
func (r *Recorder) StartStreamingToRTMP(ctx context.Context, streamURL string) error {
	if r.isRunning {
		return fmt.Errorf("recording is already running")
	}

	r.streamURL = streamURL
	log.Printf("Starting pure Go image streaming: %s", streamURL)

	// 导航到指定URL
	log.Printf("Opening URL: %s", r.config.Browser.URL)
	_, err := r.page.Goto(r.config.Browser.URL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateNetworkidle,
		Timeout:   playwright.Float(30000), // 30秒超时
	})
	if err != nil {
		return fmt.Errorf("failed to navigate to URL: %w", err)
	}

	// 等待页面加载
	log.Printf("Waiting for page to load...")
	time.Sleep(r.config.Browser.WaitTime)

	// 启动纯Go图像流
	if err := r.startPureGoStreaming(ctx); err != nil {
		return fmt.Errorf("failed to start pure Go streaming: %w", err)
	}

	r.isRunning = true
	r.isStreaming = true
	log.Printf("Pure Go image streaming started successfully")

	return nil
}

// startPureGoStreaming 启动纯Go页面捕获和图像流
func (r *Recorder) startPureGoStreaming(ctx context.Context) error {
	// 创建流上下文
	r.streamCtx, r.streamCancel = context.WithCancel(ctx)

	// 创建并启动图像流服务器
	r.imageStreamer = streaming.NewImageStreamer(r.config.RTMP.Port + 1) // 使用RTMP端口+1
	if err := r.imageStreamer.Start(); err != nil {
		return fmt.Errorf("failed to start image streamer: %w", err)
	}

	// 启动页面捕获和推流协程
	go r.captureAndStreamLoop()

	log.Printf("Pure Go image streaming started successfully")
	return nil
}

// captureAndStreamLoop 页面捕获和推流循环
func (r *Recorder) captureAndStreamLoop() {
	defer func() {
		if r.imageStreamer != nil {
			r.imageStreamer.Stop()
		}
	}()

	// 计算帧间隔
	frameInterval := time.Second / time.Duration(r.frameRate)
	ticker := time.NewTicker(frameInterval)
	defer ticker.Stop()

	log.Printf("Starting page capture loop with %d FPS", r.frameRate)

	for {
		select {
		case <-r.streamCtx.Done():
			log.Printf("Stream context cancelled, stopping capture loop")
			return
		case <-ticker.C:
			if err := r.capturePageAndSendFrame(); err != nil {
				log.Printf("Error capturing/sending frame: %v", err)
				// 继续尝试，不要因为单个帧错误而停止整个流
			}
		}
	}
}

// capturePageAndSendFrame 捕获页面帧并发送到图像流
func (r *Recorder) capturePageAndSendFrame() error {
	if r.page == nil {
		return fmt.Errorf("page not initialized")
	}

	if r.imageStreamer == nil || !r.imageStreamer.IsRunning() {
		return fmt.Errorf("image streamer not running")
	}

	// 使用Playwright截图功能捕获页面
	screenshotBytes, err := r.page.Screenshot(playwright.PageScreenshotOptions{
		Type:     playwright.ScreenshotTypePng,
		FullPage: playwright.Bool(false), // 只捕获视口
	})
	if err != nil {
		return fmt.Errorf("failed to capture page screenshot: %w", err)
	}

	// 发送到图像流服务器
	if err := r.imageStreamer.BroadcastFrame(screenshotBytes); err != nil {
		return fmt.Errorf("failed to broadcast frame: %w", err)
	}

	return nil
}



// StopRecording 停止录制
func (r *Recorder) StopRecording() error {
	if !r.isRunning {
		return fmt.Errorf("recording is not running")
	}

	log.Printf("Stopping recording...")

	// 停止纯Go推流
	if r.streamCancel != nil {
		log.Printf("Stopping pure Go streaming...")
		r.streamCancel()
	}

	// 关闭图像流服务器
	if r.imageStreamer != nil {
		if err := r.imageStreamer.Stop(); err != nil {
			log.Printf("Warning: Failed to stop image streamer: %v", err)
		}
		r.imageStreamer = nil
	}

	// 关闭页面和上下文
	if r.page != nil {
		if err := r.page.Close(); err != nil {
			log.Printf("Warning: Failed to close page: %v", err)
		}
	}

	if r.context != nil {
		if err := r.context.Close(); err != nil {
			log.Printf("Warning: Failed to close context: %v", err)
		}
	}

	r.isRunning = false
	r.isStreaming = false
	log.Printf("Recording stopped")

	return nil
}

// IsStreaming 检查是否正在推流
func (r *Recorder) IsStreaming() bool {
	r.streamMutex.RLock()
	defer r.streamMutex.RUnlock()
	return r.isStreaming && r.imageStreamer != nil && r.imageStreamer.IsRunning()
}

// GetStreamURL 获取流查看URL
func (r *Recorder) GetStreamURL() string {
	if r.imageStreamer != nil && r.imageStreamer.IsRunning() {
		return fmt.Sprintf("http://localhost:%d", r.config.RTMP.Port+1)
	}
	return ""
}

// GetStreamStats 获取流统计信息
func (r *Recorder) GetStreamStats() map[string]interface{} {
	stats := make(map[string]interface{})
	stats["is_streaming"] = r.IsStreaming()
	stats["stream_url"] = r.GetStreamURL()

	if r.imageStreamer != nil {
		stats["client_count"] = r.imageStreamer.GetClientCount()
		stats["server_running"] = r.imageStreamer.IsRunning()
	} else {
		stats["client_count"] = 0
		stats["server_running"] = false
	}

	return stats
}

// GetRTMPURL 获取当前流地址
func (r *Recorder) GetRTMPURL() string {
	return r.streamURL
}

// Cleanup 清理资源
func (r *Recorder) Cleanup() error {
	if r.isRunning {
		if err := r.StopRecording(); err != nil {
			log.Printf("Warning: Failed to stop recording: %v", err)
		}
	}

	if r.browser != nil {
		if err := r.browser.Close(); err != nil {
			log.Printf("Warning: Failed to close browser: %v", err)
		}
	}

	if r.pw != nil {
		if err := r.pw.Stop(); err != nil {
			log.Printf("Warning: Failed to stop playwright: %v", err)
		}
	}

	return nil
}

// GetRecordedVideoPath 获取录制的视频文件路径
func (r *Recorder) GetRecordedVideoPath() (string, error) {
	if r.page == nil {
		return "", fmt.Errorf("page not initialized")
	}

	// 获取视频路径
	videoPath, err := r.page.Video().Path()
	if err != nil {
		return "", fmt.Errorf("failed to get video path: %w", err)
	}

	return videoPath, nil
}

// TakeScreenshot 截图
func (r *Recorder) TakeScreenshot(filename string) error {
	if r.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 确保输出目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create screenshot directory: %w", err)
	}

	_, err := r.page.Screenshot(playwright.PageScreenshotOptions{
		Path: playwright.String(filename),
		Type: playwright.ScreenshotTypePng,
	})
	if err != nil {
		return fmt.Errorf("failed to take screenshot: %w", err)
	}

	log.Printf("Screenshot saved to: %s", filename)
	return nil
}

// IsRunning 检查是否正在录制
func (r *Recorder) IsRunning() bool {
	return r.isRunning
}

// ExecuteScript 在页面中执行JavaScript
func (r *Recorder) ExecuteScript(script string) (interface{}, error) {
	if r.page == nil {
		return nil, fmt.Errorf("page not initialized")
	}

	result, err := r.page.Evaluate(script)
	if err != nil {
		return nil, fmt.Errorf("failed to execute script: %w", err)
	}

	return result, nil
}

// WaitForElement 等待元素出现
func (r *Recorder) WaitForElement(selector string, timeout time.Duration) error {
	if r.page == nil {
		return fmt.Errorf("page not initialized")
	}

	_, err := r.page.WaitForSelector(selector, playwright.PageWaitForSelectorOptions{
		Timeout: playwright.Float(float64(timeout.Milliseconds())),
	})
	if err != nil {
		return fmt.Errorf("failed to wait for element %s: %w", selector, err)
	}

	return nil
}
