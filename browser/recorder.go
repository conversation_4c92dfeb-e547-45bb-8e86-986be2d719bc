package browser

import (
	"context"
	"fmt"
	"image"
	"image/jpeg"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"aigoplay/config"

	"github.com/kbinani/screenshot"
	"github.com/nareix/joy4/av"
	"github.com/nareix/joy4/codec/h264parser"
	"github.com/nareix/joy4/format/rtmp"
	"github.com/playwright-community/playwright-go"
)

// Recorder 浏览器录制器
type Recorder struct {
	config       *config.Config
	pw           *playwright.Playwright
	browser      playwright.Browser
	page         playwright.Page
	context      playwright.BrowserContext
	isRunning    bool
	streamingCmd *exec.Cmd
	rtmpURL      string
}

// NewRecorder 创建新的录制器
func NewRecorder(cfg *config.Config) *Recorder {
	return &Recorder{
		config:    cfg,
		isRunning: false,
	}
}

// Initialize 初始化浏览器
func (r *Recorder) Initialize(ctx context.Context) error {
	// 安装 playwright 浏览器（如果需要）
	err := playwright.Install()
	if err != nil {
		log.Printf("Warning: Failed to install playwright browsers: %v", err)
	}

	// 启动 playwright
	pw, err := playwright.Run()
	if err != nil {
		return fmt.Errorf("failed to start playwright: %w", err)
	}
	r.pw = pw

	// 启动浏览器
	browser, err := pw.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
		Headless: playwright.Bool(r.config.Browser.Headless),
		Args: []string{
			"--no-sandbox",
			"--disable-dev-shm-usage",
			"--disable-gpu",
			"--enable-webrtc-capture-all-desktop-media",
			"--auto-select-desktop-capture-source=Entire screen",
		},
	})
	if err != nil {
		return fmt.Errorf("failed to launch browser: %w", err)
	}
	r.browser = browser

	// 创建浏览器上下文
	contextOptions := playwright.BrowserNewContextOptions{
		Viewport: &playwright.Size{
			Width:  r.config.Browser.ViewportWidth,
			Height: r.config.Browser.ViewportHeight,
		},
		RecordVideo: &playwright.RecordVideo{
			Dir: r.config.Browser.RecordingPath,
		},
	}

	if r.config.Browser.UserAgent != "" {
		contextOptions.UserAgent = playwright.String(r.config.Browser.UserAgent)
	}

	browserContext, err := browser.NewContext(contextOptions)
	if err != nil {
		return fmt.Errorf("failed to create browser context: %w", err)
	}
	r.context = browserContext

	// 创建页面
	page, err := browserContext.NewPage()
	if err != nil {
		return fmt.Errorf("failed to create page: %w", err)
	}
	r.page = page

	return nil
}

// StartRecording 开始录制
func (r *Recorder) StartRecording(ctx context.Context) error {
	if r.isRunning {
		return fmt.Errorf("recording is already running")
	}

	// 确保录制目录存在
	if err := os.MkdirAll(r.config.Browser.RecordingPath, 0755); err != nil {
		return fmt.Errorf("failed to create recording directory: %w", err)
	}

	log.Printf("Opening URL: %s", r.config.Browser.URL)

	// 导航到指定URL
	_, err := r.page.Goto(r.config.Browser.URL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateNetworkidle,
		Timeout:   playwright.Float(30000), // 30秒超时
	})
	if err != nil {
		return fmt.Errorf("failed to navigate to URL: %w", err)
	}

	// 等待页面加载
	log.Printf("Waiting for page to load...")
	time.Sleep(r.config.Browser.WaitTime)

	r.isRunning = true
	log.Printf("Recording started successfully")

	return nil
}

// StartStreamingToRTMP 开始旁路录制并推流到RTMP服务器
func (r *Recorder) StartStreamingToRTMP(ctx context.Context, rtmpURL string) error {
	if r.isRunning {
		return fmt.Errorf("recording is already running")
	}

	r.rtmpURL = rtmpURL
	log.Printf("Starting streaming to RTMP: %s", rtmpURL)

	// 导航到指定URL
	log.Printf("Opening URL: %s", r.config.Browser.URL)
	_, err := r.page.Goto(r.config.Browser.URL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateNetworkidle,
		Timeout:   playwright.Float(30000), // 30秒超时
	})
	if err != nil {
		return fmt.Errorf("failed to navigate to URL: %w", err)
	}

	// 等待页面加载
	log.Printf("Waiting for page to load...")
	time.Sleep(r.config.Browser.WaitTime)

	// 启动FFmpeg进行屏幕捕获和RTMP推流
	if err := r.startFFmpegStreaming(ctx); err != nil {
		return fmt.Errorf("failed to start FFmpeg streaming: %w", err)
	}

	r.isRunning = true
	log.Printf("RTMP streaming started successfully")

	return nil
}

// startFFmpegStreaming 启动FFmpeg进行屏幕捕获和RTMP推流
func (r *Recorder) startFFmpegStreaming(ctx context.Context) error {
	// 获取浏览器窗口信息
	// 这里我们使用屏幕捕获的方式，因为Playwright的视频录制主要用于文件输出

	// FFmpeg命令参数
	args := []string{
		"-f", "avfoundation", // macOS屏幕捕获
		"-i", "1:0", // 捕获屏幕1和音频设备0
		"-vf", fmt.Sprintf("scale=%d:%d", r.config.Browser.Width, r.config.Browser.Height),
		"-c:v", "libx264",
		"-preset", "veryfast",
		"-tune", "zerolatency",
		"-c:a", "aac",
		"-b:a", "128k",
		"-f", "flv",
		r.rtmpURL,
	}

	// 如果是Linux系统，使用x11grab
	if _, err := exec.LookPath("xdpyinfo"); err == nil {
		args = []string{
			"-f", "x11grab",
			"-s", fmt.Sprintf("%dx%d", r.config.Browser.Width, r.config.Browser.Height),
			"-i", ":0.0",
			"-c:v", "libx264",
			"-preset", "veryfast",
			"-tune", "zerolatency",
			"-f", "flv",
			r.rtmpURL,
		}
	}

	log.Printf("Starting FFmpeg with args: %v", args)
	r.streamingCmd = exec.CommandContext(ctx, "ffmpeg", args...)

	// 设置输出
	r.streamingCmd.Stdout = os.Stdout
	r.streamingCmd.Stderr = os.Stderr

	// 启动FFmpeg
	if err := r.streamingCmd.Start(); err != nil {
		return fmt.Errorf("failed to start FFmpeg: %w", err)
	}

	log.Printf("FFmpeg streaming process started with PID: %d", r.streamingCmd.Process.Pid)
	return nil
}

// StopRecording 停止录制
func (r *Recorder) StopRecording() error {
	if !r.isRunning {
		return fmt.Errorf("recording is not running")
	}

	log.Printf("Stopping recording...")

	// 停止FFmpeg推流进程
	if r.streamingCmd != nil && r.streamingCmd.Process != nil {
		log.Printf("Stopping FFmpeg streaming process...")
		if err := r.streamingCmd.Process.Kill(); err != nil {
			log.Printf("Warning: Failed to kill FFmpeg process: %v", err)
		}
		r.streamingCmd.Wait() // 等待进程结束
		r.streamingCmd = nil
	}

	// 关闭页面和上下文以完成视频录制
	if r.page != nil {
		if err := r.page.Close(); err != nil {
			log.Printf("Warning: Failed to close page: %v", err)
		}
	}

	if r.context != nil {
		if err := r.context.Close(); err != nil {
			log.Printf("Warning: Failed to close context: %v", err)
		}
	}

	r.isRunning = false
	log.Printf("Recording stopped")

	return nil
}

// IsStreaming 检查是否正在推流
func (r *Recorder) IsStreaming() bool {
	return r.isRunning && r.streamingCmd != nil && r.streamingCmd.Process != nil
}

// GetRTMPURL 获取当前RTMP推流地址
func (r *Recorder) GetRTMPURL() string {
	return r.rtmpURL
}

// Cleanup 清理资源
func (r *Recorder) Cleanup() error {
	if r.isRunning {
		if err := r.StopRecording(); err != nil {
			log.Printf("Warning: Failed to stop recording: %v", err)
		}
	}

	if r.browser != nil {
		if err := r.browser.Close(); err != nil {
			log.Printf("Warning: Failed to close browser: %v", err)
		}
	}

	if r.pw != nil {
		if err := r.pw.Stop(); err != nil {
			log.Printf("Warning: Failed to stop playwright: %v", err)
		}
	}

	return nil
}

// GetRecordedVideoPath 获取录制的视频文件路径
func (r *Recorder) GetRecordedVideoPath() (string, error) {
	if r.page == nil {
		return "", fmt.Errorf("page not initialized")
	}

	// 获取视频路径
	videoPath, err := r.page.Video().Path()
	if err != nil {
		return "", fmt.Errorf("failed to get video path: %w", err)
	}

	return videoPath, nil
}

// TakeScreenshot 截图
func (r *Recorder) TakeScreenshot(filename string) error {
	if r.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 确保输出目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create screenshot directory: %w", err)
	}

	_, err := r.page.Screenshot(playwright.PageScreenshotOptions{
		Path: playwright.String(filename),
		Type: playwright.ScreenshotTypePng,
	})
	if err != nil {
		return fmt.Errorf("failed to take screenshot: %w", err)
	}

	log.Printf("Screenshot saved to: %s", filename)
	return nil
}

// IsRunning 检查是否正在录制
func (r *Recorder) IsRunning() bool {
	return r.isRunning
}

// ExecuteScript 在页面中执行JavaScript
func (r *Recorder) ExecuteScript(script string) (interface{}, error) {
	if r.page == nil {
		return nil, fmt.Errorf("page not initialized")
	}

	result, err := r.page.Evaluate(script)
	if err != nil {
		return nil, fmt.Errorf("failed to execute script: %w", err)
	}

	return result, nil
}

// WaitForElement 等待元素出现
func (r *Recorder) WaitForElement(selector string, timeout time.Duration) error {
	if r.page == nil {
		return fmt.Errorf("page not initialized")
	}

	_, err := r.page.WaitForSelector(selector, playwright.PageWaitForSelectorOptions{
		Timeout: playwright.Float(float64(timeout.Milliseconds())),
	})
	if err != nil {
		return fmt.Errorf("failed to wait for element %s: %w", selector, err)
	}

	return nil
}
